# Skin86 示例应用配置
server:
  port: 8080

# Skin86 配置
skin86:
  # API配置
  api:
    app-id: ${SKIN86_APP_ID:002}                    # 从环境变量获取或使用默认值
    app-secret: ${SKIN86_APP_SECRET:E3E80D27D09873BB85B2BB1B9C3618A5}  # 从环境变量获取或使用默认值
    base-url: ${SKIN86_BASE_URL:https://csdata-api.skin86.com}
    timeout: 30s
    
  # 性能配置
  performance:
    max-concurrency: 5                              # 降低并发数，适合示例应用
    delay-ms: 500ms                                 # 增加延迟，避免触发限流
    retry-times: 3
    retry-delay: 2s
    batch-size: 100                                 # 减小批次大小
    
  # 平台配置
  platform:
    supported: [yp, buff, steam, igxe]
    default-platforms: [yp]

# 日志配置
logging:
  level:
    com.skin86: INFO
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"