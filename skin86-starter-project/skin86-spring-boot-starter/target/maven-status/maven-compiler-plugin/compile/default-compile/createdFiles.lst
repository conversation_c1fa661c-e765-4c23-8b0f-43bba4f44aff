com\skin86\starter\autoconfigure\Skin86AutoConfiguration$WebConfiguration.class
com\skin86\starter\dto\ApiResponse.class
com\skin86\starter\dto\response\TodayPriceResponse.class
com\skin86\starter\client\Skin86ApiClient$2.class
com\skin86\starter\autoconfigure\Skin86AutoConfiguration$PersistenceConfiguration.class
com\skin86\starter\client\Skin86ApiClient$1.class
com\skin86\starter\autoconfigure\Skin86Properties$PlatformConfig.class
com\skin86\starter\dto\SkinGoodsInfo.class
com\skin86\starter\autoconfigure\Skin86Properties.class
com\skin86\starter\dto\request\TodayPriceRequest.class
com\skin86\starter\client\Skin86ApiClient$3.class
com\skin86\starter\service\Skin86DataService.class
com\skin86\starter\service\Skin86DataServiceImpl.class
com\skin86\starter\autoconfigure\Skin86AutoConfiguration.class
com\skin86\starter\dto\response\BatchPricesResponse.class
com\skin86\starter\autoconfigure\Skin86Properties$ApiConfig.class
com\skin86\starter\dto\request\GoodsListRequest.class
com\skin86\starter\client\Skin86ApiClient.class
com\skin86\starter\dto\request\BatchPricesRequest.class
com\skin86\starter\dto\SkinPriceInfo.class
com\skin86\starter\autoconfigure\Skin86Properties$PerformanceConfig.class
com\skin86\starter\dto\response\GoodsListResponse.class
