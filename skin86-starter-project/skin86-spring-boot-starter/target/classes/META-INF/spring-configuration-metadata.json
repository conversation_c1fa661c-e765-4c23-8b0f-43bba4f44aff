{"groups": [{"name": "skin86", "type": "com.skin86.starter.autoconfigure.Skin86Properties", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties"}, {"name": "skin86.api", "type": "com.skin86.starter.autoconfigure.Skin86Properties$ApiConfig", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties", "sourceMethod": "api()"}, {"name": "skin86.performance", "type": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties", "sourceMethod": "performance()"}, {"name": "skin86.platform", "type": "com.skin86.starter.autoconfigure.Skin86Properties$PlatformConfig", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties", "sourceMethod": "platform()"}], "properties": [{"name": "skin86.performance.batch-size", "type": "java.lang.Integer", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig", "defaultValue": 0}, {"name": "skin86.performance.delay-ms", "type": "java.time.Duration", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig"}, {"name": "skin86.performance.max-concurrency", "type": "java.lang.Integer", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig", "defaultValue": 0}, {"name": "skin86.performance.retry-delay", "type": "java.time.Duration", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig"}, {"name": "skin86.performance.retry-times", "type": "java.lang.Integer", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PerformanceConfig", "defaultValue": 0}, {"name": "skin86.platform.default-platforms", "type": "java.util.List<java.lang.String>", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PlatformConfig"}, {"name": "skin86.platform.supported", "type": "java.util.List<java.lang.String>", "sourceType": "com.skin86.starter.autoconfigure.Skin86Properties$PlatformConfig"}], "hints": []}