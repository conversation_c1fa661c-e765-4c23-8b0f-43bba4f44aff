package com.skin86.starter.autoconfigure;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.util.List;

/**
 * Skin86 Starter 配置属性
 * 
 * 简化版的配置属性，提供最常用的配置选项和合理的默认值
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@ConfigurationProperties(prefix = "skin86")
@Validated
public record Skin86Properties(
    @Valid @NotNull ApiConfig api,
    @Valid @NotNull PerformanceConfig performance,
    @Valid @NotNull PlatformConfig platform,
    @Valid @NotNull PersistenceConfig persistence
) {

    /**
     * API相关配置
     */
    public record ApiConfig(
        @NotBlank String appId,
        @NotBlank String appSecret,
        @NotBlank String baseUrl,
        @NotNull Duration timeout
    ) {
        public ApiConfig {
            // 提供默认值
            if (baseUrl == null || baseUrl.isBlank()) {
                baseUrl = "https://csdata-api.skin86.com";
            }
            if (timeout == null) {
                timeout = Duration.ofSeconds(30);
            }
        }
        
        // 带默认值的构造方法
        public ApiConfig(String appId, String appSecret) {
            this(appId, appSecret, "https://csdata-api.skin86.com", Duration.ofSeconds(30));
        }
    }

    /**
     * 性能相关配置
     */
    public record PerformanceConfig(
        @Min(1) @Max(50) int maxConcurrency,
        @NotNull Duration delayMs,
        @Min(1) @Max(10) int retryTimes,
        @NotNull Duration retryDelay,
        @Min(10) @Max(2000) int batchSize
    ) {
        public PerformanceConfig {
            // 提供默认值
            if (delayMs == null) {
                delayMs = Duration.ofMillis(200);
            }
            if (retryDelay == null) {
                retryDelay = Duration.ofSeconds(2);
            }
        }
        
        // 默认配置构造方法
        public static PerformanceConfig defaultConfig() {
            return new PerformanceConfig(
                10,                          // maxConcurrency
                Duration.ofMillis(200),      // delayMs
                3,                           // retryTimes
                Duration.ofSeconds(2),       // retryDelay
                500                          // batchSize
            );
        }
    }

    /**
     * 平台相关配置
     */
    public record PlatformConfig(
        @NotNull List<String> supported,
        @NotNull List<String> defaultPlatforms
    ) {
        public PlatformConfig {
            // 提供默认值
            if (supported == null || supported.isEmpty()) {
                supported = List.of("yp", "buff", "steam", "igxe");
            }
            if (defaultPlatforms == null || defaultPlatforms.isEmpty()) {
                defaultPlatforms = List.of("yp");
            }
        }
        
        // 默认配置构造方法
        public static PlatformConfig defaultConfig() {
            return new PlatformConfig(
                List.of("yp", "buff", "steam", "igxe"),
                List.of("yp")
            );
        }
    }

    /**
     * 数据库持久化相关配置
     */
    public record PersistenceConfig(
        boolean enabled,
        boolean autoSaveGoods,
        boolean autoSavePrices,
        @NotBlank String goodsTableName,
        @NotBlank String priceHistoryTableName,
        @Min(1) @Max(10000) Integer batchSize,
        @NotNull Duration asyncTimeout,
        boolean enableTransactionLog,
        @Min(1) @Max(10) Integer maxRetryAttempts,
        @NotNull Duration retryDelay
    ) {
        public PersistenceConfig {
            // 提供默认值
            if (goodsTableName == null || goodsTableName.isBlank()) {
                goodsTableName = "skin_goods";
            }
            if (priceHistoryTableName == null || priceHistoryTableName.isBlank()) {
                priceHistoryTableName = "price_history";
            }
            if (batchSize == null || batchSize <= 0) {
                batchSize = 100;
            }
            if (asyncTimeout == null) {
                asyncTimeout = Duration.ofSeconds(30);
            }
            if (maxRetryAttempts == null || maxRetryAttempts <= 0) {
                maxRetryAttempts = 3;
            }
            if (retryDelay == null) {
                retryDelay = Duration.ofSeconds(2);
            }
        }

        // 默认配置构造方法
        public static PersistenceConfig defaultConfig() {
            return new PersistenceConfig(
                false,                          // 默认不启用持久化
                true,                           // 启用时自动保存商品数据
                true,                           // 启用时自动保存价格数据
                "skin_goods",                   // 默认商品表名
                "price_history",                // 默认价格历史表名
                100,                            // 默认批处理大小
                Duration.ofSeconds(30),         // 默认异步超时时间
                false,                          // 默认不启用事务日志
                3,                              // 默认最大重试次数
                Duration.ofSeconds(2)           // 默认重试延迟
            );
        }
    }

    // 创建默认配置的静态方法
    public static Skin86Properties createDefault(String appId, String appSecret) {
        return new Skin86Properties(
            new ApiConfig(appId, appSecret),
            PerformanceConfig.defaultConfig(),
            PlatformConfig.defaultConfig(),
            PersistenceConfig.defaultConfig()
        );
    }
}