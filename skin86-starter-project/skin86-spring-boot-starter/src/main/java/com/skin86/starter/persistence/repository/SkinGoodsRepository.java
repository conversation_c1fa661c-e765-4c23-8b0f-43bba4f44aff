package com.skin86.starter.persistence.repository;

import com.skin86.starter.persistence.entity.SkinGoods;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 饰品商品数据访问接口
 * 
 * 提供饰品商品数据的CRUD操作和自定义查询方法
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Repository
public interface SkinGoodsRepository extends JpaRepository<SkinGoods, Integer> {

    /**
     * 根据商品ID和平台查找商品
     * 
     * @param goodsId 商品ID
     * @param platform 平台名称
     * @return 商品信息
     */
    Optional<SkinGoods> findByGoodsIdAndPlatform(Integer goodsId, String platform);

    /**
     * 根据商品ID列表和平台查找商品
     * 
     * @param goodsIds 商品ID列表
     * @param platform 平台名称
     * @return 商品列表
     */
    List<SkinGoods> findByGoodsIdInAndPlatform(List<Integer> goodsIds, String platform);

    /**
     * 根据平台查找所有商品
     * 
     * @param platform 平台名称
     * @return 商品列表
     */
    List<SkinGoods> findByPlatform(String platform);

    /**
     * 根据平台分页查找商品
     * 
     * @param platform 平台名称
     * @param pageable 分页参数
     * @return 分页商品列表
     */
    Page<SkinGoods> findByPlatform(String platform, Pageable pageable);

    /**
     * 根据市场哈希名称模糊查找商品
     * 
     * @param marketHashName 市场哈希名称
     * @param platform 平台名称
     * @return 商品列表
     */
    List<SkinGoods> findByMarketHashNameContainingIgnoreCaseAndPlatform(String marketHashName, String platform);

    /**
     * 根据稀有度查找商品
     * 
     * @param rarity 稀有度
     * @param platform 平台名称
     * @return 商品列表
     */
    List<SkinGoods> findByRarityAndPlatform(String rarity, String platform);

    /**
     * 根据价格范围查找商品
     * 
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param platform 平台名称
     * @return 商品列表
     */
    @Query("SELECT sg FROM SkinGoods sg WHERE sg.platform = :platform " +
           "AND ((sg.sellMinPrice BETWEEN :minPrice AND :maxPrice) " +
           "OR (sg.buyMaxPrice BETWEEN :minPrice AND :maxPrice))")
    List<SkinGoods> findByPriceRangeAndPlatform(
        @Param("minPrice") BigDecimal minPrice,
        @Param("maxPrice") BigDecimal maxPrice,
        @Param("platform") String platform
    );

    /**
     * 统计平台商品数量
     * 
     * @param platform 平台名称
     * @return 商品数量
     */
    long countByPlatform(String platform);

    /**
     * 检查商品是否存在
     * 
     * @param goodsId 商品ID
     * @param platform 平台名称
     * @return 是否存在
     */
    boolean existsByGoodsIdAndPlatform(Integer goodsId, String platform);

    /**
     * 批量插入或更新饰品数据
     * 使用MySQL的ON DUPLICATE KEY UPDATE语法实现高性能的批量upsert操作
     * 
     * @param goodsId 商品ID
     * @param platformId 平台ID
     * @param marketName 市场名称
     * @param marketHashName 市场哈希名称
     * @param sellMinPrice 最低售价
     * @param sellMaxNum 最大售出数量
     * @param sellValuation 售出估值
     * @param buyMaxPrice 最高收购价
     * @param buyMaxNum 最大收购数量
     * @param priceAlterPercentage7d 7天价格变化百分比
     * @param priceAlterValue7d 7天价格变化值
     * @param categoryGroupName 分类组名称
     * @param rarityColor 稀有度颜色
     * @param iconUrl 图标URL
     * @param isFollow 是否关注
     * @param redirectUrl 重定向URL
     * @param exterior 外观
     * @param rarity 稀有度
     * @param platform 平台
     */
    @Transactional
    @Modifying
    @Query(value = """
            INSERT INTO skin_goods 
            (goods_id, platform_id, market_name, market_hash_name, sell_min_price,
             sell_max_num, sell_valuation, buy_max_price, buy_max_num,
             price_alter_percentage_7d, price_alter_value_7d, category_group_name,
             rarity_color, icon_url, is_follow, redirect_url, exterior, rarity, platform,
             created_at, updated_at)
            VALUES (:goodsId, :platformId, :marketName, :marketHashName, :sellMinPrice,
                    :sellMaxNum, :sellValuation, :buyMaxPrice, :buyMaxNum,
                    :priceAlterPercentage7d, :priceAlterValue7d, :categoryGroupName,
                    :rarityColor, :iconUrl, :isFollow, :redirectUrl, :exterior, :rarity, :platform,
                    NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            platform_id = VALUES(platform_id),
            market_name = VALUES(market_name),
            market_hash_name = VALUES(market_hash_name),
            sell_min_price = VALUES(sell_min_price),
            sell_max_num = VALUES(sell_max_num),
            sell_valuation = VALUES(sell_valuation),
            buy_max_price = VALUES(buy_max_price),
            buy_max_num = VALUES(buy_max_num),
            price_alter_percentage_7d = VALUES(price_alter_percentage_7d),
            price_alter_value_7d = VALUES(price_alter_value_7d),
            category_group_name = VALUES(category_group_name),
            rarity_color = VALUES(rarity_color),
            icon_url = VALUES(icon_url),
            is_follow = VALUES(is_follow),
            redirect_url = VALUES(redirect_url),
            exterior = VALUES(exterior),
            rarity = VALUES(rarity),
            updated_at = NOW()
            """, nativeQuery = true)
    void upsertSkinGoods(
            @Param("goodsId") Integer goodsId,
            @Param("platformId") String platformId,
            @Param("marketName") String marketName,
            @Param("marketHashName") String marketHashName,
            @Param("sellMinPrice") BigDecimal sellMinPrice,
            @Param("sellMaxNum") Integer sellMaxNum,
            @Param("sellValuation") BigDecimal sellValuation,
            @Param("buyMaxPrice") BigDecimal buyMaxPrice,
            @Param("buyMaxNum") Integer buyMaxNum,
            @Param("priceAlterPercentage7d") BigDecimal priceAlterPercentage7d,
            @Param("priceAlterValue7d") BigDecimal priceAlterValue7d,
            @Param("categoryGroupName") String categoryGroupName,
            @Param("rarityColor") String rarityColor,
            @Param("iconUrl") String iconUrl,
            @Param("isFollow") Boolean isFollow,
            @Param("redirectUrl") String redirectUrl,
            @Param("exterior") String exterior,
            @Param("rarity") String rarity,
            @Param("platform") String platform
    );

    /**
     * 根据平台删除商品（软删除，实际是更新操作）
     * 
     * @param platform 平台名称
     * @return 影响的行数
     */
    @Transactional
    @Modifying
    @Query("UPDATE SkinGoods sg SET sg.updatedAt = CURRENT_TIMESTAMP WHERE sg.platform = :platform")
    int markAsUpdatedByPlatform(@Param("platform") String platform);

    /**
     * 获取平台统计信息
     * 
     * @return 平台统计信息列表 [platform, count]
     */
    @Query("SELECT sg.platform, COUNT(sg) FROM SkinGoods sg GROUP BY sg.platform")
    List<Object[]> getPlatformStatistics();

    /**
     * 查找有效价格的商品
     * 
     * @param platform 平台名称
     * @return 有效价格的商品列表
     */
    @Query("SELECT sg FROM SkinGoods sg WHERE sg.platform = :platform " +
           "AND (sg.sellMinPrice > 0 OR sg.buyMaxPrice > 0)")
    List<SkinGoods> findValidPriceGoodsByPlatform(@Param("platform") String platform);

    /**
     * 查找最近更新的商品
     * 
     * @param platform 平台名称
     * @param limit 限制数量
     * @return 最近更新的商品列表
     */
    @Query(value = "SELECT * FROM skin_goods WHERE platform = :platform " +
                   "ORDER BY updated_at DESC LIMIT :limit", nativeQuery = true)
    List<SkinGoods> findRecentlyUpdatedGoods(@Param("platform") String platform, @Param("limit") int limit);
}
