package com.skin86.starter.persistence.repository;

import com.skin86.starter.persistence.entity.PriceHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格历史数据访问接口
 * 
 * 提供价格历史数据的CRUD操作和自定义查询方法
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Repository
public interface PriceHistoryRepository extends JpaRepository<PriceHistory, Long> {

    /**
     * 根据商品ID查找价格历史
     * 
     * @param skinGoodsId 商品ID
     * @return 价格历史列表
     */
    List<PriceHistory> findBySkinGoodsIdAndDeletedOrderByRecordTimeDesc(Integer skinGoodsId, Byte deleted);

    /**
     * 根据商品ID和数据源查找价格历史
     * 
     * @param skinGoodsId 商品ID
     * @param dataSource 数据源
     * @return 价格历史列表
     */
    List<PriceHistory> findBySkinGoodsIdAndDataSourceAndDeletedOrderByRecordTimeDesc(
        Integer skinGoodsId, String dataSource, Byte deleted);

    /**
     * 根据数据源查找价格历史
     * 
     * @param dataSource 数据源
     * @param pageable 分页参数
     * @return 分页价格历史列表
     */
    Page<PriceHistory> findByDataSourceAndDeletedOrderByRecordTimeDesc(
        String dataSource, Byte deleted, Pageable pageable);

    /**
     * 根据时间范围查找价格历史
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param dataSource 数据源
     * @return 价格历史列表
     */
    List<PriceHistory> findByRecordTimeBetweenAndDataSourceAndDeletedOrderByRecordTimeDesc(
        LocalDateTime startTime, LocalDateTime endTime, String dataSource, Byte deleted);

    /**
     * 根据商品ID和时间范围查找价格历史
     * 
     * @param skinGoodsId 商品ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 价格历史列表
     */
    List<PriceHistory> findBySkinGoodsIdAndRecordTimeBetweenAndDeletedOrderByRecordTimeDesc(
        Integer skinGoodsId, LocalDateTime startTime, LocalDateTime endTime, Byte deleted);

    /**
     * 统计商品的价格历史记录数量
     * 
     * @param skinGoodsId 商品ID
     * @param deleted 删除标记
     * @return 记录数量
     */
    long countBySkinGoodsIdAndDeleted(Integer skinGoodsId, Byte deleted);

    /**
     * 统计数据源的价格历史记录数量
     * 
     * @param dataSource 数据源
     * @param deleted 删除标记
     * @return 记录数量
     */
    long countByDataSourceAndDeleted(String dataSource, Byte deleted);

    /**
     * 查找最新的价格记录
     * 
     * @param skinGoodsId 商品ID
     * @param dataSource 数据源
     * @param deleted 删除标记
     * @return 最新价格记录
     */
    @Query("SELECT ph FROM PriceHistory ph WHERE ph.skinGoodsId = :skinGoodsId " +
           "AND ph.dataSource = :dataSource AND ph.deleted = :deleted " +
           "ORDER BY ph.recordTime DESC LIMIT 1")
    PriceHistory findLatestPriceHistory(
        @Param("skinGoodsId") Integer skinGoodsId,
        @Param("dataSource") String dataSource,
        @Param("deleted") Byte deleted
    );

    /**
     * 批量插入价格历史记录
     * 
     * @param skinGoodsId 饰品ID
     * @param goodsName 商品名称
     * @param price 价格
     * @param priceUsd 美元价格
     * @param priceChange 价格变化
     * @param priceChangePercent 价格变化百分比
     * @param volume 交易量
     * @param dataSource 数据源
     * @param recordType 记录类型
     * @param recordTime 记录时间
     * @param exchangeRate 汇率
     * @param trend 趋势
     * @param volatility 波动性
     * @param isAbnormal 是否异常
     * @param abnormalReason 异常原因
     * @param dataQualityScore 数据质量评分
     * @param extraInfo 扩展信息
     * @param createdBy 创建者
     * @param updatedBy 更新者
     * @param remark 备注
     */
    @Transactional
    @Modifying
    @Query(value = """
        INSERT INTO price_history 
        (skin_goods_id, goods_name, price, price_usd, price_change, price_change_percent,
         volume, data_source, record_type, record_time, exchange_rate, trend,
         volatility, is_abnormal, abnormal_reason, data_quality_score, extra_info,
         created_by, updated_by, remark, deleted, created_at, updated_at)
        VALUES (:skinGoodsId, :goodsName, :price, :priceUsd, :priceChange, :priceChangePercent,
                :volume, :dataSource, :recordType, :recordTime, :exchangeRate, :trend,
                :volatility, :isAbnormal, :abnormalReason, :dataQualityScore, :extraInfo,
                :createdBy, :updatedBy, :remark, 0, NOW(), NOW())
        """, nativeQuery = true)
    void insertPriceHistory(
        @Param("skinGoodsId") Integer skinGoodsId,
        @Param("goodsName") String goodsName,
        @Param("price") BigDecimal price,
        @Param("priceUsd") BigDecimal priceUsd,
        @Param("priceChange") BigDecimal priceChange,
        @Param("priceChangePercent") BigDecimal priceChangePercent,
        @Param("volume") Integer volume,
        @Param("dataSource") String dataSource,
        @Param("recordType") Byte recordType,
        @Param("recordTime") LocalDateTime recordTime,
        @Param("exchangeRate") BigDecimal exchangeRate,
        @Param("trend") Byte trend,
        @Param("volatility") BigDecimal volatility,
        @Param("isAbnormal") Byte isAbnormal,
        @Param("abnormalReason") String abnormalReason,
        @Param("dataQualityScore") Byte dataQualityScore,
        @Param("extraInfo") String extraInfo,
        @Param("createdBy") String createdBy,
        @Param("updatedBy") String updatedBy,
        @Param("remark") String remark
    );

    /**
     * 根据记录类型查找价格历史
     * 
     * @param recordType 记录类型
     * @param dataSource 数据源
     * @param deleted 删除标记
     * @return 价格历史列表
     */
    List<PriceHistory> findByRecordTypeAndDataSourceAndDeletedOrderByRecordTimeDesc(
        Byte recordType, String dataSource, Byte deleted);

    /**
     * 查找异常价格记录
     * 
     * @param dataSource 数据源
     * @param deleted 删除标记
     * @return 异常价格记录列表
     */
    List<PriceHistory> findByIsAbnormalAndDataSourceAndDeletedOrderByRecordTimeDesc(
        Byte isAbnormal, String dataSource, Byte deleted);

    /**
     * 根据价格范围查找历史记录
     * 
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param dataSource 数据源
     * @param deleted 删除标记
     * @return 价格历史列表
     */
    List<PriceHistory> findByPriceBetweenAndDataSourceAndDeletedOrderByRecordTimeDesc(
        BigDecimal minPrice, BigDecimal maxPrice, String dataSource, Byte deleted);

    /**
     * 获取数据源统计信息
     * 
     * @return 数据源统计信息列表 [dataSource, count]
     */
    @Query("SELECT ph.dataSource, COUNT(ph) FROM PriceHistory ph WHERE ph.deleted = 0 GROUP BY ph.dataSource")
    List<Object[]> getDataSourceStatistics();

    /**
     * 获取商品价格趋势数据
     * 
     * @param skinGoodsId 商品ID
     * @param dataSource 数据源
     * @param limit 限制数量
     * @return 价格趋势数据
     */
    @Query(value = """
        SELECT record_time, price, price_change, price_change_percent 
        FROM price_history 
        WHERE skin_goods_id = :skinGoodsId AND data_source = :dataSource AND deleted = 0
        ORDER BY record_time DESC 
        LIMIT :limit
        """, nativeQuery = true)
    List<Object[]> getPriceTrendData(
        @Param("skinGoodsId") Integer skinGoodsId,
        @Param("dataSource") String dataSource,
        @Param("limit") int limit
    );

    /**
     * 清理过期的价格历史记录（软删除）
     * 
     * @param beforeTime 时间点之前的记录
     * @return 影响的行数
     */
    @Transactional
    @Modifying
    @Query("UPDATE PriceHistory ph SET ph.deleted = 1, ph.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE ph.recordTime < :beforeTime AND ph.deleted = 0")
    int markAsDeletedBeforeTime(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 获取最近的价格变化记录
     * 
     * @param dataSource 数据源
     * @param hours 小时数
     * @return 最近的价格变化记录
     */
    @Query(value = """
        SELECT * FROM price_history 
        WHERE data_source = :dataSource AND deleted = 0 
        AND record_time >= DATE_SUB(NOW(), INTERVAL :hours HOUR)
        AND price_change IS NOT NULL 
        ORDER BY record_time DESC
        """, nativeQuery = true)
    List<PriceHistory> findRecentPriceChanges(@Param("dataSource") String dataSource, @Param("hours") int hours);
}
