# Skin86 API数据获取系统配置文件
# 支持多环境配置和外部配置覆盖

# 服务器配置
server:
  port: ${SERVER_PORT:8989}                                  # 服务端口

spring:
  application:
    name: skin86-api-fetcher
  # 数据库配置
  datasource:
    url: ${DB_URL:*************************************************************************************************************************************************************************************************************************************************************}
    username: ${DB_USERNAME:vimbox}
    password: ${DB_PASSWORD:Vimbox123@}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: ${DB_CONNECTION_POOL_SIZE:50}  # 大幅增加连接池：30 → 50
      minimum-idle: 20                                  # 增加最小空闲连接：10 → 20
      connection-timeout: 20000                         # 减少连接超时：30s → 20s
      idle-timeout: 180000                              # 减少空闲超时：5min → 3min
      max-lifetime: 1200000                             # 减少连接生命周期：30min → 20min
      leak-detection-threshold: 30000                   # 减少泄漏检测阈值：60s → 30s
      
  # JPA配置 - 批量插入优化
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true                                     # 启用SQL日志验证批量插入
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
        # 高性能批量插入优化配置
        jdbc:
          batch_size: 2000                    # 大幅增加批量大小：1000 → 2000
          batch_versioned_data: true          # 启用版本化数据批处理
          fetch_size: 1000                    # 设置获取大小
        order_inserts: true                   # 排序INSERT语句
        order_updates: true                   # 排序UPDATE语句
        batch_fetch_style: DYNAMIC            # 动态批量获取
        generate_statistics: false           # 关闭统计以提升性能
        cache:
          use_second_level_cache: false      # 关闭二级缓存
          use_query_cache: false             # 关闭查询缓存
        use_sql_comments: true
        
  # Flyway数据库迁移
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    
  # 虚拟线程支持
  threads:
    virtual:
      enabled: true
  profiles:
    active: dev

# 应用监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  prometheus:
    metrics:
      export:
        enabled: true

# 日志配置
logging:
  level:
    com.skin86: ${LOG_LEVEL:INFO}
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_DIR:logs}/skin86-api-fetcher.log

# Skin86系统配置
skin86:
  # API配置
  api:
    app-id: ${API_APP_ID:002}
    app-secret: ${API_APP_SECRET:E3E80D27D09873BB85B2BB1B9C3618A5}
    base-url: ${API_BASE_URL:https://csdata-api.skin86.com}
    timeout: ${API_TIMEOUT:30s}
    
  # 数据库配置
  database:
    url: ${spring.datasource.url}
    username: ${spring.datasource.username}
    password: ${spring.datasource.password}
    connection-pool-size: ${spring.datasource.hikari.connection-pool-size:20}
    connection-timeout: ${DB_CONNECTION_TIMEOUT:60s}
    transaction-timeout: ${DB_TRANSACTION_TIMEOUT:5m}
    
  # 性能配置 - 高性能优化版
  performance:
    max-concurrency: ${API_MAX_CONCURRENCY:20}        # 大幅增加并发度：10 → 20
    delay-ms: ${API_DELAY_MS:100ms}                    # 大幅减少延迟：200ms → 100ms
    retry-times: ${API_RETRY_TIMES:3}
    retry-delay: ${API_RETRY_DELAY:1s}                 # 减少重试延迟：2s → 1s
    goods-batch-size: ${DB_GOODS_BATCH_SIZE:500}      # 增加批次大小：100 → 500
    price-batch-size: ${DB_PRICE_BATCH_SIZE:500}       # 增加批次大小：200 → 500
    db-batch-size: ${DB_BATCH_SIZE:1000}               # 增加数据库批次：200 → 1000
    
  # 调度器配置
  scheduler:
    # 定时任务执行时间：
    # - 商品数据获取：每天上午9点和下午6点 (cron: "0 0 9,18 * * ?")
    # - 数据清理任务：每天凌晨2点 (cron: "0 0 2 * * ?")
    default-interval-hours: ${SCHEDULER_INTERVAL:12}          # 保留字段，向后兼容
    enable-checkpoint: ${ENABLE_CHECKPOINT:true}
    checkpoint-interval-minutes: ${CHECKPOINT_INTERVAL:10}
    log-file: ${SCHEDULER_LOG_FILE:scheduler.log}
    pid-file: ${SCHEDULER_PID_FILE:scheduler.pid}
    checkpoint-file: ${CHECKPOINT_FILE:checkpoints.json}
    # 数据更新策略配置
    goods-full-update-hours: ${GOODS_FULL_UPDATE_HOURS:24}    # 强制全量更新间隔（小时）
    goods-freshness-hours: ${GOODS_FRESHNESS_HOURS:12}        # 商品数据新鲜度阈值（小时）
    checkpoint-expire-hours: ${CHECKPOINT_EXPIRE_HOURS:6}     # 断点过期时间（小时）

    # 启动时数据获取配置
    startup:
      enabled: ${STARTUP_FETCH_ENABLED:false}                  # 是否启用启动时自动获取数据
      delay-seconds: ${STARTUP_DELAY_SECONDS:5}               # 启动延迟时间（秒）
      platforms: ${STARTUP_PLATFORMS:yp}                      # 启动时获取的平台列表，逗号分隔
      force-full-update: ${STARTUP_FORCE_FULL:false}          # 是否强制全量更新
      timeout-minutes: ${STARTUP_TIMEOUT_MINUTES:30}          # 启动任务超时时间（分钟）
    
  # 平台配置
  platform:
    supported:
      - yp
      - buff
      - steam
      - igex
    default-platforms: [yp]
    buff:
      max-concurrency: 5
      delay-ms: 500ms
      batch-size: 500
    igxe:
      max-concurrency: 5
      delay-ms: 500ms
      batch-size: 500
    steam:
      max-concurrency: 5
      delay-ms: 500ms
      batch-size: 500
    yp:
      max-concurrency: 10      # 增加YP平台并发度
      delay-ms: 200ms          # 减少YP平台延迟
      batch-size: 1000         # 增加YP平台批次大小

  # 安全配置
  security:
    # API Key认证配置
    api-key:
      enabled: ${API_KEY_ENABLED:false}                        # 是否启用API Key认证
      header-name: ${API_KEY_HEADER:X-API-Key}               # API Key请求头名称
      valid-keys:                                             # 有效的API Key列表
        - ${API_KEY_1:sk-skin86-dev-12345678}                # 开发环境API Key
        - ${API_KEY_2:sk-skin86-prod-87654321}               # 生产环境API Key

    # 请求频率限制配置
    rate-limit:
      enabled: ${RATE_LIMIT_ENABLED:true}                     # 是否启用频率限制
      requests-per-minute: ${RATE_LIMIT_MINUTE:60}            # 每分钟最大请求数
      requests-per-hour: ${RATE_LIMIT_HOUR:1000}              # 每小时最大请求数
      requests-per-day: ${RATE_LIMIT_DAY:10000}               # 每天最大请求数

    # IP白名单配置
    ip-whitelist:
      enabled: ${IP_WHITELIST_ENABLED:false}                  # 是否启用IP白名单
      allowed-ips:                                            # 允许的IP地址列表
        - ${ALLOWED_IP_1:127.0.0.1}                          # 本地IP
        - ${ALLOWED_IP_2:::1}                                 # IPv6本地IP
      
  # 监控配置
  format:
    date-pattern: yyyy-MM-dd
  monitoring:
    enable-stats: ${ENABLE_STATS:true}
    stats-interval: ${STATS_INTERVAL:10s}
    enable-memory-monitor: ${ENABLE_MEMORY_MONITOR:true}
    memory-threshold: ${MEMORY_THRESHOLD:0.8}
    log-level: ${LOG_LEVEL:INFO}
    enable-file-logging: ${ENABLE_FILE_LOGGING:true}
    log-dir: ${LOG_DIR:logs}
    max-file-size: ${LOG_MAX_FILE_SIZE:10MB}
    max-files: ${LOG_MAX_FILES:5}