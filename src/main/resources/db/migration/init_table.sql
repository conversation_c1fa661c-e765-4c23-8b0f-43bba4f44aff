CREATE TABLE `price_history` (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `skin_goods_id` int NOT NULL COMMENT '皮肤商品ID',
                                 `goods_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称(冗余字段)',
                                 `price` decimal(12,2) NOT NULL COMMENT '价格(人民币)',
                                 `price_usd` decimal(12,2) DEFAULT NULL COMMENT '价格(美元)',
                                 `price_change` decimal(12,2) DEFAULT NULL COMMENT '与前一次价格的变化',
                                 `price_change_percent` decimal(8,4) DEFAULT NULL COMMENT '与前一次价格的变化百分比',
                                 `volume` int DEFAULT NULL COMMENT '交易量',
                                 `data_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '数据来源(steam,buff163,c5game等)',
                                 `record_type` tinyint NOT NULL COMMENT '记录类型(1:定时采集,2:手动更新,3:价格预警触发)',
                                 `record_time` datetime NOT NULL COMMENT '记录时间',
                                 `exchange_rate` decimal(8,4) DEFAULT NULL COMMENT '汇率(美元对人民币)',
                                 `trend` tinyint DEFAULT NULL COMMENT '市场趋势(1:上涨,0:持平,-1:下跌)',
                                 `volatility` decimal(8,4) DEFAULT NULL COMMENT '价格波动性(标准差)',
                                 `is_abnormal` tinyint DEFAULT '0' COMMENT '是否为异常价格(0:否,1:是)',
                                 `abnormal_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '异常原因',
                                 `data_quality_score` tinyint DEFAULT '90' COMMENT '数据质量评分(1-100)',
                                 `extra_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '扩展信息(JSON格式)',
                                 `created_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建者',
                                 `updated_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新者',
                                 `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
                                 `deleted` tinyint DEFAULT '0' COMMENT '是否删除(0:否,1:是)',
                                 `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_price_history_skin_goods_id` (`skin_goods_id`),
                                 KEY `idx_price_history_data_source` (`data_source`),
                                 KEY `idx_price_history_record_time` (`record_time`),
                                 KEY `idx_price_history_deleted` (`deleted`),
                                 KEY `idx_price_history_goods_source_time` (`skin_goods_id`,`data_source`,`record_time`,`deleted`),
                                 KEY `idx_price_history_source_time` (`data_source`,`record_time`,`deleted`),
                                 KEY `idx_price_history_type_time` (`record_type`,`record_time`,`deleted`),
                                 KEY `idx_price_history_price_time` (`price`,`record_time`),
                                 CONSTRAINT `fk_price_history_skin_goods_id` FOREIGN KEY (`skin_goods_id`) REFERENCES `skin_goods` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=258 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='价格历史记录表';
CREATE TABLE `skin_goods` (
                              `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                              `goods_id` int NOT NULL COMMENT '商品ID',
                              `platform_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台ID',
                              `market_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '市场名称',
                              `market_hash_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '市场哈希名称',
                              `sell_min_price` decimal(10,2) DEFAULT NULL COMMENT '最低售价',
                              `sell_max_num` int DEFAULT NULL COMMENT '最大售出数量',
                              `sell_valuation` decimal(15,2) DEFAULT NULL COMMENT '售出估值',
                              `buy_max_price` decimal(10,2) DEFAULT NULL COMMENT '最高收购价',
                              `buy_max_num` int DEFAULT NULL COMMENT '最大收购数量',
                              `price_alter_percentage_7d` decimal(10,4) DEFAULT NULL COMMENT '7天价格变化百分比',
                              `price_alter_value_7d` decimal(10,2) DEFAULT NULL COMMENT '7天价格变化值',
                              `category_group_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类组名称',
                              `rarity_color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '稀有度颜色',
                              `icon_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标URL',
                              `is_follow` tinyint(1) DEFAULT NULL COMMENT '是否关注',
                              `redirect_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '重定向URL',
                              `exterior` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '外观',
                              `rarity` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '稀有度',
                              `platform` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台',
                              `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_goods_platform` (`goods_id`,`platform`),
                              KEY `idx_goods_id` (`goods_id`),
                              KEY `idx_platform` (`platform`),
                              KEY `idx_market_hash_name` (`market_hash_name`),
                              KEY `idx_rarity` (`rarity`),
                              KEY `idx_category_group_name` (`category_group_name`),
                              KEY `idx_created_at` (`created_at`),
                              KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4975 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='饰品商品主表';