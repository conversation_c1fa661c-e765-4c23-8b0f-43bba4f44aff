package com.skin86.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 获取饰品列表请求
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record GoodsListRequest(
    @JsonProperty("search")
    String search,
    
    @JsonProperty("platform")
    @NotBlank(message = "平台不能为空")
    String platform,
    
    @JsonProperty("page")
    @Min(value = 1, message = "页码必须大于0")
    int page,
    
    @JsonProperty("page_size")
    @Min(value = 1, message = "页面大小必须大于0")
    int pageSize
) {
    
    /**
     * 创建请求实例
     * 
     * @param platform 平台
     * @param page 页码
     * @param pageSize 页面大小
     * @return 请求实例
     */
    public static GoodsListRequest of(String platform, int page, int pageSize) {
        return new GoodsListRequest("", platform, page, pageSize); // 使用空字符串而不是null
    }
    
    /**
     * 创建带搜索条件的请求实例
     * 
     * @param search 搜索关键词
     * @param platform 平台
     * @param page 页码
     * @param pageSize 页面大小
     * @return 请求实例
     */
    public static GoodsListRequest of(String search, String platform, int page, int pageSize) {
        return new GoodsListRequest(search, platform, page, pageSize);
    }
}
