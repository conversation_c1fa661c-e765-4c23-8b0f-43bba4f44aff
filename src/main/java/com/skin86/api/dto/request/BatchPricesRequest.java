package com.skin86.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;

/**
 * 批量获取饰品价格请求
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record BatchPricesRequest(
    @JsonProperty("platform")
    @NotBlank(message = "平台不能为空")
    String platform,
    
    @JsonProperty("page")
    @Min(value = 1, message = "页码必须大于0")
    int page,
    
    @JsonProperty("page_size")
    @Min(value = 1, message = "页面大小必须大于0")
    int pageSize,
    
    @JsonProperty("date")
    @NotBlank(message = "日期不能为空")
    String date,
    
    @JsonProperty("goods_ids")
    @NotBlank(message = "商品ID列表不能为空")
    String goodsIds
) {
    
    /**
     * 创建请求实例
     * 
     * @param platform 平台
     * @param page 页码
     * @param pageSize 页面大小
     * @param date 日期
     * @param goodsIds 商品ID列表（逗号分隔）
     * @return 请求实例
     */
    public static BatchPricesRequest of(String platform, int page, int pageSize, String date, String goodsIds) {
        return new BatchPricesRequest(platform, page, pageSize, date, goodsIds);
    }
}
