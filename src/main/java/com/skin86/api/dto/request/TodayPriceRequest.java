package com.skin86.api.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;

/**
 * 获取指定饰品价格请求
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record TodayPriceRequest(
    @JsonProperty("market_hash_name")
    @NotBlank(message = "市场哈希名称不能为空")
    String marketHashName,
    
    @JsonProperty("platform")
    @NotBlank(message = "平台不能为空")
    String platform,
    
    @JsonProperty("date")
    String date,
    
    @JsonProperty("style_name")
    String styleName
) {
    
    /**
     * 创建请求实例
     * 
     * @param marketHashName 市场哈希名称
     * @param platform 平台
     * @return 请求实例
     */
    public static TodayPriceRequest of(String marketHashName, String platform) {
        return new TodayPriceRequest(marketHashName, platform, null, null);
    }
    
    /**
     * 创建带日期的请求实例
     * 
     * @param marketHashName 市场哈希名称
     * @param platform 平台
     * @param date 日期
     * @return 请求实例
     */
    public static TodayPriceRequest of(String marketHashName, String platform, String date) {
        return new TodayPriceRequest(marketHashName, platform, date, null);
    }
}
