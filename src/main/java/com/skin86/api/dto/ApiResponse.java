package com.skin86.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * API响应通用包装类
 * 使用Java 21 Record类简化响应数据结构
 * 提供类型安全的API响应处理
 * @param <T> 响应数据类型
 * <AUTHOR> Team
 * @version 1.0
 */
public record ApiResponse<T>(
    @JsonProperty("code") int code,
    @JsonProperty("message") String message,
    @JsonProperty("data") T data,
    @JsonProperty("timestamp") Long timestamp
) {
    
    /**
     * 判断响应是否成功
     * Skin86 API使用0表示成功，非0表示失败
     *
     * @return 成功返回true，失败返回false
     */
    public boolean isSuccess() {
        return code == 0;
    }
    
    /**
     * 获取错误信息
     * 
     * @return 错误信息，成功时返回null
     */
    public String getErrorMessage() {
        return isSuccess() ? null : message;
    }
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "success", data, System.currentTimeMillis());
    }
    
    /**
     * 创建失败响应
     * 
     * @param code 错误码
     * @param message 错误信息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null, System.currentTimeMillis());
    }
}
