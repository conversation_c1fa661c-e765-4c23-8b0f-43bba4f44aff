package com.skin86.api.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 获取饰品列表响应
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record GoodsListResponse(
    @JsonProperty("list") List<GoodsItem> list,
    @JsonProperty("total") Integer total,
    @JsonProperty("page") Integer page,
    @JsonProperty("pageSize") Integer pageSize
) {
    /**
     * 商品列表项
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public record GoodsItem(
        @JsonProperty("id") Integer id,
        @JsonProperty("name") String name,
        @JsonProperty("goods_id") Long goodsId,
        @JsonProperty("platform_id") String platformId,
        @JsonProperty("market_name") String marketName,
        @JsonProperty("market_hash_name") String marketHashName,
        @JsonProperty("sell_min_price") BigDecimal sellMinPrice,
        @JsonProperty("sell_max_num") Integer sellMaxNum,
        @JsonProperty("sell_valuation") BigDecimal sellValuation,
        @JsonProperty("buy_max_price") BigDecimal buyMaxPrice,
        @JsonProperty("buy_max_num") Integer buyMaxNum,
        @JsonProperty("price_alter_percentage_7d") BigDecimal priceAlterPercentage7d,
        @JsonProperty("price_alter_value_7d") BigDecimal priceAlterValue7d,
        @JsonProperty("category_group_name") String categoryGroupName,
        @JsonProperty("rarity_color") String rarityColor,
        @JsonProperty("icon_url") String iconUrl,
        @JsonProperty("is_follow") Boolean isFollow,
        @JsonProperty("redirect_url") String redirectUrl,
        @JsonProperty("exterior") String exterior,
        @JsonProperty("rarity") String rarity,
        @JsonProperty("steam_price_cny") String steamPriceCny,
        @JsonProperty("delivery_time_text") String deliveryTimeText
    ) {}
}
