package com.skin86.api.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批量获取饰品价格响应
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record BatchPricesResponse(
    @JsonProperty("items")
    List<PriceItem> list,

    @JsonProperty("total")
    Long total,

    @JsonProperty("page")
    Integer page,

    @JsonProperty("page_size")
    Integer pageSize
) {
    
    /**
     * 价格项目
     */
    public record PriceItem(
        @JsonProperty("goods_id")
        Long goodsId,

        @JsonProperty("market_hash_name")
        String marketHashName,

        @JsonProperty("price")
        BigDecimal price,

        @JsonProperty("max_price")
        BigDecimal maxPrice,

        @JsonProperty("min_price")
        BigDecimal minPrice,

        @JsonProperty("date")
        String date
    ) {}
}
