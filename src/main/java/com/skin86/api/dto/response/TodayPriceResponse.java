package com.skin86.api.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

/**
 * 获取指定饰品价格响应
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record TodayPriceResponse(
    @JsonProperty("goods_id")
    Long goodsId,
    
    @JsonProperty("market_hash_name")
    String marketHashName,
    
    @JsonProperty("platform")
    String platform,
    
    @JsonProperty("price")
    BigDecimal price,
    
    @JsonProperty("price_usd")
    BigDecimal priceUsd,
    
    @JsonProperty("volume")
    Integer volume,
    
    @JsonProperty("date")
    String date,
    
    @JsonProperty("style_name")
    String styleName
) {}
