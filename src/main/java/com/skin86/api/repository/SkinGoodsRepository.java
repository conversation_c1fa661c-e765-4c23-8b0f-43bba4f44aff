package com.skin86.api.repository;

import com.skin86.api.entity.SkinGoods;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 饰品商品数据访问接口
 * <p>
 * 提供饰品数据的CRUD操作和自定义查询方法
 * 支持批量操作和高性能查询
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Repository
public interface SkinGoodsRepository extends JpaRepository<SkinGoods, Integer> {

    /**
     * 根据商品ID和平台查找饰品
     *
     * @param goodsId  商品ID
     * @param platform 平台名称
     * @return 饰品信息
     */
    Optional<SkinGoods> findByGoodsIdAndPlatform(Integer goodsId, String platform);

    /**
     * 批量查询商品ID到数据库主键ID的映射
     * 解决N+1查询问题
     *
     * @param goodsIds 商品ID列表
     * @param platform 平台名称
     * @return 商品ID到数据库主键ID的映射
     */
    @Query("SELECT s.goodsId, s.id FROM SkinGoods s WHERE s.goodsId IN :goodsIds AND s.platform = :platform")
    List<Object[]> findGoodsIdToDbIdMapping(@Param("goodsIds") List<Integer> goodsIds, @Param("platform") String platform);

    /**
     * 根据平台查找所有饰品
     *
     * @param platform 平台名称
     * @param pageable 分页参数
     * @return 饰品列表
     */
    Page<SkinGoods> findByPlatform(String platform, Pageable pageable);

    /**
     * 根据市场哈希名称模糊查询
     *
     * @param marketHashName 市场哈希名称
     * @param pageable       分页参数
     * @return 饰品列表
     */
    Page<SkinGoods> findByMarketHashNameContainingIgnoreCase(String marketHashName, Pageable pageable);

    /**
     * 根据平台和市场哈希名称查询
     *
     * @param platform       平台名称
     * @param marketHashName 市场哈希名称
     * @param pageable       分页参数
     * @return 饰品列表
     */
    Page<SkinGoods> findByPlatformAndMarketHashNameContainingIgnoreCase(
            String platform, String marketHashName, Pageable pageable);

    /**
     * 获取指定平台的所有商品ID
     *
     * @param platform 平台名称
     * @return 商品ID列表
     */
    @Query("SELECT s.goodsId FROM SkinGoods s WHERE s.platform = :platform")
    List<Integer> findGoodsIdsByPlatform(@Param("platform") String platform);

    /**
     * 统计指定平台的商品数量
     *
     * @param platform 平台名称
     * @return 商品数量
     */
    long countByPlatform(String platform);

    /**
     * 根据goods_id列表和平台查询商品
     *
     * @param goodsIds 商品ID列表
     * @param platform 平台名称
     * @return 商品列表
     */
    List<SkinGoods> findByGoodsIdInAndPlatform(List<Integer> goodsIds, String platform);

    /**
     * 批量插入或更新饰品数据
     * <p>
     * 使用MySQL的ON DUPLICATE KEY UPDATE语法实现高性能的批量upsert操作
     *
     * @param goodsId                商品ID
     * @param platformId             平台ID
     * @param marketName             市场名称
     * @param marketHashName         市场哈希名称
     * @param sellMinPrice           最低售价
     * @param sellMaxNum             最大售出数量
     * @param sellValuation          售出估值
     * @param buyMaxPrice            最高收购价
     * @param buyMaxNum              最大收购数量
     * @param priceAlterPercentage7d 7天价格变化百分比
     * @param priceAlterValue7d      7天价格变化值
     * @param categoryGroupName      分类组名称
     * @param rarityColor            稀有度颜色
     * @param iconUrl                图标URL
     * @param isFollow               是否关注
     * @param redirectUrl            重定向URL
     * @param exterior               外观
     * @param rarity                 稀有度
     * @param platform               平台
     */
    @Transactional
    @Modifying
    @Query(value = """
            INSERT INTO skin_goods 
            (goods_id, platform_id, market_name, market_hash_name, sell_min_price,
             sell_max_num, sell_valuation, buy_max_price, buy_max_num,
             price_alter_percentage_7d, price_alter_value_7d, category_group_name,
             rarity_color, icon_url, is_follow, redirect_url, exterior, rarity, platform,
             created_at, updated_at)
            VALUES (:goodsId, :platformId, :marketName, :marketHashName, :sellMinPrice,
                    :sellMaxNum, :sellValuation, :buyMaxPrice, :buyMaxNum,
                    :priceAlterPercentage7d, :priceAlterValue7d, :categoryGroupName,
                    :rarityColor, :iconUrl, :isFollow, :redirectUrl, :exterior, :rarity, :platform,
                    NOW(), NOW())
            ON DUPLICATE KEY UPDATE
            platform_id = VALUES(platform_id),
            market_name = VALUES(market_name),
            market_hash_name = VALUES(market_hash_name),
            sell_min_price = VALUES(sell_min_price),
            sell_max_num = VALUES(sell_max_num),
            sell_valuation = VALUES(sell_valuation),
            buy_max_price = VALUES(buy_max_price),
            buy_max_num = VALUES(buy_max_num),
            price_alter_percentage_7d = VALUES(price_alter_percentage_7d),
            price_alter_value_7d = VALUES(price_alter_value_7d),
            category_group_name = VALUES(category_group_name),
            rarity_color = VALUES(rarity_color),
            icon_url = VALUES(icon_url),
            is_follow = VALUES(is_follow),
            redirect_url = VALUES(redirect_url),
            exterior = VALUES(exterior),
            rarity = VALUES(rarity),
            updated_at = NOW()
            """, nativeQuery = true)
    void upsertSkinGoods(
            @Param("goodsId") Integer goodsId,
            @Param("platformId") String platformId,
            @Param("marketName") String marketName,
            @Param("marketHashName") String marketHashName,
            @Param("sellMinPrice") java.math.BigDecimal sellMinPrice,
            @Param("sellMaxNum") Integer sellMaxNum,
            @Param("sellValuation") java.math.BigDecimal sellValuation,
            @Param("buyMaxPrice") java.math.BigDecimal buyMaxPrice,
            @Param("buyMaxNum") Integer buyMaxNum,
            @Param("priceAlterPercentage7d") java.math.BigDecimal priceAlterPercentage7d,
            @Param("priceAlterValue7d") java.math.BigDecimal priceAlterValue7d,
            @Param("categoryGroupName") String categoryGroupName,
            @Param("rarityColor") String rarityColor,
            @Param("iconUrl") String iconUrl,
            @Param("isFollow") Boolean isFollow,
            @Param("redirectUrl") String redirectUrl,
            @Param("exterior") String exterior,
            @Param("rarity") String rarity,
            @Param("platform") String platform
    );

    /**
     * 根据稀有度查询饰品
     *
     * @param rarity   稀有度
     * @param pageable 分页参数
     * @return 饰品列表
     */
    Page<SkinGoods> findByRarity(String rarity, Pageable pageable);

    /**
     * 根据分类组查询饰品
     *
     * @param categoryGroupName 分类组名称
     * @param pageable          分页参数
     * @return 饰品列表
     */
    Page<SkinGoods> findByCategoryGroupName(String categoryGroupName, Pageable pageable);

    /**
     * 查询价格范围内的饰品
     *
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @param pageable 分页参数
     * @return 饰品列表
     */
    @Query("SELECT s FROM SkinGoods s WHERE s.sellMinPrice BETWEEN :minPrice AND :maxPrice")
    Page<SkinGoods> findByPriceRange(
            @Param("minPrice") java.math.BigDecimal minPrice,
            @Param("maxPrice") java.math.BigDecimal maxPrice,
            Pageable pageable
    );

    /**
     * 删除指定平台的所有数据
     *
     * @param platform 平台名称
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM SkinGoods s WHERE s.platform = :platform")
    @Transactional
    int deleteByPlatform(@Param("platform") String platform);
}
