package com.skin86.api.repository;

import com.skin86.api.entity.PriceHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格历史数据访问接口
 * 
 * 提供价格历史数据的CRUD操作和统计查询方法
 * 支持时间序列数据的高效查询和批量操作
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Repository
public interface PriceHistoryRepository extends JpaRepository<PriceHistory, Long> {

    /**
     * 根据饰品ID查询价格历史
     * 
     * @param skinGoodsId 饰品ID
     * @param pageable 分页参数
     * @return 价格历史列表
     */
    Page<PriceHistory> findBySkinGoodsIdAndDeletedOrderByRecordTimeDesc(
        Integer skinGoodsId, Byte deleted, Pageable pageable);

    /**
     * 根据饰品ID和数据源查询价格历史
     * 
     * @param skinGoodsId 饰品ID
     * @param dataSource 数据源
     * @param deleted 删除标记
     * @param pageable 分页参数
     * @return 价格历史列表
     */
    Page<PriceHistory> findBySkinGoodsIdAndDataSourceAndDeletedOrderByRecordTimeDesc(
        Integer skinGoodsId, String dataSource, Byte deleted, Pageable pageable);

    /**
     * 根据时间范围查询价格历史
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 价格历史列表
     */
    Page<PriceHistory> findByRecordTimeBetweenAndDeletedOrderByRecordTimeDesc(
        LocalDateTime startTime, LocalDateTime endTime, Byte deleted, Pageable pageable);

    /**
     * 获取指定饰品的最新价格记录
     * 
     * @param skinGoodsId 饰品ID
     * @param dataSource 数据源
     * @return 最新价格记录
     */
    @Query("SELECT p FROM PriceHistory p WHERE p.skinGoodsId = :skinGoodsId " +
           "AND p.dataSource = :dataSource AND p.deleted = 0 " +
           "ORDER BY p.recordTime DESC LIMIT 1")
    PriceHistory findLatestPriceBySkinGoodsIdAndDataSource(
        @Param("skinGoodsId") Integer skinGoodsId,
        @Param("dataSource") String dataSource);

    /**
     * 统计指定时间范围内的价格记录数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param dataSource 数据源
     * @return 记录数量
     */
    @Query("SELECT COUNT(p) FROM PriceHistory p WHERE p.recordTime BETWEEN :startTime AND :endTime " +
           "AND p.dataSource = :dataSource AND p.deleted = 0")
    long countByRecordTimeBetweenAndDataSource(
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("dataSource") String dataSource);

    /**
     * 获取价格趋势统计
     * 
     * @param skinGoodsId 饰品ID
     * @param dataSource 数据源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 价格统计信息
     */
    @Query("SELECT MIN(p.price) as minPrice, MAX(p.price) as maxPrice, " +
           "AVG(p.price) as avgPrice, COUNT(p) as recordCount " +
           "FROM PriceHistory p WHERE p.skinGoodsId = :skinGoodsId " +
           "AND p.dataSource = :dataSource AND p.deleted = 0 " +
           "AND p.recordTime BETWEEN :startTime AND :endTime")
    Object[] getPriceTrendStats(
        @Param("skinGoodsId") Integer skinGoodsId,
        @Param("dataSource") String dataSource,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 批量插入价格历史记录
     * 
     * @param skinGoodsId 饰品ID
     * @param goodsName 商品名称
     * @param price 价格
     * @param priceUsd 美元价格
     * @param priceChange 价格变化
     * @param priceChangePercent 价格变化百分比
     * @param volume 交易量
     * @param dataSource 数据源
     * @param recordType 记录类型
     * @param recordTime 记录时间
     * @param exchangeRate 汇率
     * @param trend 趋势
     * @param volatility 波动性
     * @param isAbnormal 是否异常
     * @param abnormalReason 异常原因
     * @param dataQualityScore 数据质量评分
     * @param extraInfo 扩展信息
     * @param createdBy 创建者
     * @param updatedBy 更新者
     * @param remark 备注
     */
    @Transactional
    @Modifying
    @Query(value = """
        INSERT INTO price_history 
        (skin_goods_id, goods_name, price, price_usd, price_change, price_change_percent,
         volume, data_source, record_type, record_time, exchange_rate, trend,
         volatility, is_abnormal, abnormal_reason, data_quality_score, extra_info,
         created_by, updated_by, remark, deleted, created_at, updated_at)
        VALUES (:skinGoodsId, :goodsName, :price, :priceUsd, :priceChange, :priceChangePercent,
                :volume, :dataSource, :recordType, :recordTime, :exchangeRate, :trend,
                :volatility, :isAbnormal, :abnormalReason, :dataQualityScore, :extraInfo,
                :createdBy, :updatedBy, :remark, 0, NOW(), NOW())
        """, nativeQuery = true)
    void insertPriceHistory(
        @Param("skinGoodsId") Integer skinGoodsId,
        @Param("goodsName") String goodsName,
        @Param("price") BigDecimal price,
        @Param("priceUsd") BigDecimal priceUsd,
        @Param("priceChange") BigDecimal priceChange,
        @Param("priceChangePercent") BigDecimal priceChangePercent,
        @Param("volume") Integer volume,
        @Param("dataSource") String dataSource,
        @Param("recordType") Byte recordType,
        @Param("recordTime") LocalDateTime recordTime,
        @Param("exchangeRate") BigDecimal exchangeRate,
        @Param("trend") Byte trend,
        @Param("volatility") BigDecimal volatility,
        @Param("isAbnormal") Byte isAbnormal,
        @Param("abnormalReason") String abnormalReason,
        @Param("dataQualityScore") Byte dataQualityScore,
        @Param("extraInfo") String extraInfo,
        @Param("createdBy") String createdBy,
        @Param("updatedBy") String updatedBy,
        @Param("remark") String remark
    );

    /**
     * 查询异常价格记录
     * 
     * @param dataSource 数据源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 异常价格记录列表
     */
    Page<PriceHistory> findByDataSourceAndIsAbnormalAndRecordTimeBetweenAndDeletedOrderByRecordTimeDesc(
        String dataSource, Byte isAbnormal, LocalDateTime startTime, LocalDateTime endTime, 
        Byte deleted, Pageable pageable);

    /**
     * 查询低质量数据记录
     * 
     * @param maxQualityScore 最大质量评分
     * @param dataSource 数据源
     * @param pageable 分页参数
     * @return 低质量数据记录列表
     */
    @Query("SELECT p FROM PriceHistory p WHERE p.dataQualityScore <= :maxQualityScore " +
           "AND p.dataSource = :dataSource AND p.deleted = 0 " +
           "ORDER BY p.dataQualityScore ASC, p.recordTime DESC")
    Page<PriceHistory> findLowQualityRecords(
        @Param("maxQualityScore") Byte maxQualityScore,
        @Param("dataSource") String dataSource,
        Pageable pageable);

    /**
     * 删除指定时间之前的历史记录
     * 
     * @param beforeTime 时间阈值
     * @return 删除的记录数
     */
    @Modifying
    @Query("UPDATE PriceHistory p SET p.deleted = 1, p.updatedAt = NOW() " +
           "WHERE p.recordTime < :beforeTime AND p.deleted = 0")
    int softDeleteRecordsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 物理删除已标记删除的记录
     * 
     * @return 删除的记录数
     */
    @Modifying
    @Query("DELETE FROM PriceHistory p WHERE p.deleted = 1")
    int physicalDeleteMarkedRecords();

    /**
     * 获取数据源统计信息
     *
     * @param dataSource 数据源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    @Query("SELECT COUNT(p) as totalRecords, " +
           "COUNT(CASE WHEN p.isAbnormal = 1 THEN 1 END) as abnormalRecords, " +
           "AVG(p.dataQualityScore) as avgQualityScore, " +
           "MIN(p.recordTime) as earliestRecord, " +
           "MAX(p.recordTime) as latestRecord " +
           "FROM PriceHistory p WHERE p.dataSource = :dataSource " +
           "AND p.recordTime BETWEEN :startTime AND :endTime AND p.deleted = 0")
    Object[] getDataSourceStats(
        @Param("dataSource") String dataSource,
        @Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 真正的批量插入价格历史记录
     * 使用JPA的saveAll方法实现批量插入
     *
     * @param priceHistories 价格历史记录列表
     * @return 保存的记录列表
     */
    @Override
    @Transactional
    <S extends PriceHistory> List<S> saveAll(Iterable<S> priceHistories);
}
