package com.skin86.api.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 饰品商品实体类
 * 
 * 对应数据库中的skin_goods表
 * 存储CS2饰品的基本信息和价格数据
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Entity
@Table(name = "skin_goods", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"goods_id", "platform"}),
       indexes = {
           @Index(name = "idx_goods_id", columnList = "goods_id"),
           @Index(name = "idx_platform", columnList = "platform"),
           @Index(name = "idx_market_hash_name", columnList = "market_hash_name")
       })
public class SkinGoods {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "goods_id", nullable = false)
    private Integer goodsId;

    @Column(name = "platform_id", length = 50)
    private String platformId;

    @Column(name = "market_name", nullable = false)
    private String marketName;

    @Column(name = "market_hash_name", nullable = false)
    private String marketHashName;

    @Column(name = "sell_min_price", precision = 10, scale = 2)
    private BigDecimal sellMinPrice;

    @Column(name = "sell_max_num")
    private Integer sellMaxNum;

    @Column(name = "sell_valuation", precision = 15, scale = 2)
    private BigDecimal sellValuation;

    @Column(name = "buy_max_price", precision = 10, scale = 2)
    private BigDecimal buyMaxPrice;

    @Column(name = "buy_max_num")
    private Integer buyMaxNum;

    @Column(name = "price_alter_percentage_7d", precision = 10, scale = 4)
    private BigDecimal priceAlterPercentage7d;

    @Column(name = "price_alter_value_7d", precision = 10, scale = 2)
    private BigDecimal priceAlterValue7d;

    @Column(name = "category_group_name", length = 100)
    private String categoryGroupName;

    @Column(name = "rarity_color", length = 20)
    private String rarityColor;

    @Column(name = "icon_url", length = 500)
    private String iconUrl;

    @Column(name = "is_follow")
    private Boolean isFollow;

    @Column(name = "redirect_url", length = 500)
    private String redirectUrl;

    @Column(name = "exterior", length = 50)
    private String exterior;

    @Column(name = "rarity", length = 50)
    private String rarity;

    @Column(name = "platform", length = 20, nullable = false)
    private String platform;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 默认构造函数
    public SkinGoods() {}

    // 构造函数
    public SkinGoods(Integer goodsId, String platform, String marketName, String marketHashName) {
        this.goodsId = goodsId;
        this.platform = platform;
        this.marketName = marketName;
        this.marketHashName = marketHashName;
    }

    // Getters and Setters


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Boolean getFollow() {
        return isFollow;
    }

    public void setFollow(Boolean follow) {
        isFollow = follow;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getMarketName() {
        return marketName;
    }

    public void setMarketName(String marketName) {
        this.marketName = marketName;
    }

    public String getMarketHashName() {
        return marketHashName;
    }

    public void setMarketHashName(String marketHashName) {
        this.marketHashName = marketHashName;
    }

    public BigDecimal getSellMinPrice() {
        return sellMinPrice;
    }

    public void setSellMinPrice(BigDecimal sellMinPrice) {
        this.sellMinPrice = sellMinPrice;
    }

    public Integer getSellMaxNum() {
        return sellMaxNum;
    }

    public void setSellMaxNum(Integer sellMaxNum) {
        this.sellMaxNum = sellMaxNum;
    }

    public BigDecimal getSellValuation() {
        return sellValuation;
    }

    public void setSellValuation(BigDecimal sellValuation) {
        this.sellValuation = sellValuation;
    }

    public BigDecimal getBuyMaxPrice() {
        return buyMaxPrice;
    }

    public void setBuyMaxPrice(BigDecimal buyMaxPrice) {
        this.buyMaxPrice = buyMaxPrice;
    }

    public Integer getBuyMaxNum() {
        return buyMaxNum;
    }

    public void setBuyMaxNum(Integer buyMaxNum) {
        this.buyMaxNum = buyMaxNum;
    }

    public BigDecimal getPriceAlterPercentage7d() {
        return priceAlterPercentage7d;
    }

    public void setPriceAlterPercentage7d(BigDecimal priceAlterPercentage7d) {
        this.priceAlterPercentage7d = priceAlterPercentage7d;
    }

    public BigDecimal getPriceAlterValue7d() {
        return priceAlterValue7d;
    }

    public void setPriceAlterValue7d(BigDecimal priceAlterValue7d) {
        this.priceAlterValue7d = priceAlterValue7d;
    }

    public String getCategoryGroupName() {
        return categoryGroupName;
    }

    public void setCategoryGroupName(String categoryGroupName) {
        this.categoryGroupName = categoryGroupName;
    }

    public String getRarityColor() {
        return rarityColor;
    }

    public void setRarityColor(String rarityColor) {
        this.rarityColor = rarityColor;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Boolean getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(Boolean isFollow) {
        this.isFollow = isFollow;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getExterior() {
        return exterior;
    }

    public void setExterior(String exterior) {
        this.exterior = exterior;
    }

    public String getRarity() {
        return rarity;
    }

    public void setRarity(String rarity) {
        this.rarity = rarity;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkinGoods skinGoods = (SkinGoods) o;
        return Objects.equals(goodsId, skinGoods.goodsId) && 
               Objects.equals(platform, skinGoods.platform);
    }

    @Override
    public int hashCode() {
        return Objects.hash(goodsId, platform);
    }

    @Override
    public String toString() {
        return "SkinGoods{" +
                "id=" + id +
                ", goodsId=" + goodsId +
                ", platform='" + platform + '\'' +
                ", marketName='" + marketName + '\'' +
                ", marketHashName='" + marketHashName + '\'' +
                ", sellMinPrice=" + sellMinPrice +
                '}';
    }
}
