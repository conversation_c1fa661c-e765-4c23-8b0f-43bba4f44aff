package com.skin86.api.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 价格历史实体类
 * 
 * 对应数据库中的price_history表
 * 存储饰品价格的历史变化记录
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Entity
@Table(name = "price_history",
       indexes = {
           @Index(name = "idx_skin_goods_id", columnList = "skin_goods_id"),
           @Index(name = "idx_data_source", columnList = "data_source"),
           @Index(name = "idx_record_time", columnList = "record_time"),
           @Index(name = "idx_price_time", columnList = "price,record_time")
       })
public class PriceHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "skin_goods_id", nullable = false)
    private Integer skinGoodsId;

    @Column(name = "goods_name", nullable = false, length = 300)
    private String goodsName;

    @Column(name = "price", nullable = false, precision = 12, scale = 2)
    private BigDecimal price;

    @Column(name = "price_usd", precision = 12, scale = 2)
    private BigDecimal priceUsd;

    @Column(name = "price_change", precision = 12, scale = 2)
    private BigDecimal priceChange;

    @Column(name = "price_change_percent", precision = 8, scale = 4)
    private BigDecimal priceChangePercent;

    @Column(name = "volume")
    private Integer volume;

    @Column(name = "data_source", nullable = false, length = 50)
    private String dataSource;

    @Column(name = "record_type", nullable = false)
    private Byte recordType;

    @Column(name = "record_time", nullable = false)
    private LocalDateTime recordTime;

    @Column(name = "exchange_rate", precision = 8, scale = 4)
    private BigDecimal exchangeRate;

    @Column(name = "trend")
    private Byte trend;

    @Column(name = "volatility", precision = 8, scale = 4)
    private BigDecimal volatility;

    @Column(name = "is_abnormal", columnDefinition = "TINYINT DEFAULT 0")
    private Byte isAbnormal = 0;

    @Column(name = "abnormal_reason", length = 200)
    private String abnormalReason;

    @Column(name = "data_quality_score", columnDefinition = "TINYINT DEFAULT 90")
    private Byte dataQualityScore = 90;

    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "remark", length = 500)
    private String remark;

    @Column(name = "deleted", columnDefinition = "TINYINT DEFAULT 0")
    private Byte deleted = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 关联到SkinGoods实体
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "skin_goods_id", insertable = false, updatable = false)
    private SkinGoods skinGoods;

    // 默认构造函数
    public PriceHistory() {}

    // 构造函数
    public PriceHistory(Integer skinGoodsId, String goodsName, BigDecimal price,
                       String dataSource, Byte recordType, LocalDateTime recordTime) {
        this.skinGoodsId = skinGoodsId;
        this.goodsName = goodsName;
        this.price = price;
        this.dataSource = dataSource;
        this.recordType = recordType;
        this.recordTime = recordTime;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSkinGoodsId() {
        return skinGoodsId;
    }

    public void setSkinGoodsId(Integer skinGoodsId) {
        this.skinGoodsId = skinGoodsId;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPriceUsd() {
        return priceUsd;
    }

    public void setPriceUsd(BigDecimal priceUsd) {
        this.priceUsd = priceUsd;
    }

    public BigDecimal getPriceChange() {
        return priceChange;
    }

    public void setPriceChange(BigDecimal priceChange) {
        this.priceChange = priceChange;
    }

    public BigDecimal getPriceChangePercent() {
        return priceChangePercent;
    }

    public void setPriceChangePercent(BigDecimal priceChangePercent) {
        this.priceChangePercent = priceChangePercent;
    }

    public Integer getVolume() {
        return volume;
    }

    public void setVolume(Integer volume) {
        this.volume = volume;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public Byte getRecordType() {
        return recordType;
    }

    public void setRecordType(Byte recordType) {
        this.recordType = recordType;
    }

    public LocalDateTime getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(LocalDateTime recordTime) {
        this.recordTime = recordTime;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public Byte getTrend() {
        return trend;
    }

    public void setTrend(Byte trend) {
        this.trend = trend;
    }

    public BigDecimal getVolatility() {
        return volatility;
    }

    public void setVolatility(BigDecimal volatility) {
        this.volatility = volatility;
    }

    public Byte getIsAbnormal() {
        return isAbnormal;
    }

    public void setIsAbnormal(Byte isAbnormal) {
        this.isAbnormal = isAbnormal;
    }

    public String getAbnormalReason() {
        return abnormalReason;
    }

    public void setAbnormalReason(String abnormalReason) {
        this.abnormalReason = abnormalReason;
    }

    public Byte getDataQualityScore() {
        return dataQualityScore;
    }

    public void setDataQualityScore(Byte dataQualityScore) {
        this.dataQualityScore = dataQualityScore;
    }

    public String getExtraInfo() {
        return extraInfo;
    }

    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Byte getDeleted() {
        return deleted;
    }

    public void setDeleted(Byte deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public SkinGoods getSkinGoods() {
        return skinGoods;
    }

    public void setSkinGoods(SkinGoods skinGoods) {
        this.skinGoods = skinGoods;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PriceHistory that = (PriceHistory) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "PriceHistory{" +
                "id=" + id +
                ", skinGoodsId=" + skinGoodsId +
                ", goodsName='" + goodsName + '\'' +
                ", price=" + price +
                ", dataSource='" + dataSource + '\'' +
                ", recordTime=" + recordTime +
                '}';
    }
}
