package com.skin86.api.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.skin86.api.config.SecurityConfig;
import com.skin86.api.dto.ApiResponse;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 请求频率限制过滤器
 * 实现基于IP的请求频率限制，防止API滥用
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class RateLimitingFilter extends OncePerRequestFilter {

    private final SecurityConfig.SecurityProperties securityProperties;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Map<String, RateLimitInfo> rateLimitMap = new ConcurrentHashMap<>();

    public RateLimitingFilter(SecurityConfig.SecurityProperties securityProperties) {
        this.securityProperties = securityProperties;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {

        if (!securityProperties.getRateLimit().isEnabled()) {
            filterChain.doFilter(request, response);
            return;
        }

        String clientIp = getClientIpAddress(request);
        String requestPath = request.getRequestURI();

        // 跳过不需要限制的路径
        if (shouldSkipRateLimit(requestPath)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 检查频率限制
        if (!checkRateLimit(clientIp)) {
            sendRateLimitExceededResponse(response, clientIp);
            return;
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 检查请求频率限制
     * 
     * @param clientIp 客户端IP
     * @return 是否允许请求
     */
    private boolean checkRateLimit(String clientIp) {
        RateLimitInfo rateLimitInfo = rateLimitMap.computeIfAbsent(clientIp, k -> new RateLimitInfo());
        
        LocalDateTime now = LocalDateTime.now();
        
        // 清理过期的计数器
        rateLimitInfo.cleanupExpiredCounters(now);
        
        // 检查每分钟限制
        if (rateLimitInfo.getRequestsInLastMinute(now) >= securityProperties.getRateLimit().getRequestsPerMinute()) {
            return false;
        }
        
        // 检查每小时限制
        if (rateLimitInfo.getRequestsInLastHour(now) >= securityProperties.getRateLimit().getRequestsPerHour()) {
            return false;
        }
        
        // 检查每天限制
        if (rateLimitInfo.getRequestsInLastDay(now) >= securityProperties.getRateLimit().getRequestsPerDay()) {
            return false;
        }
        
        // 记录请求
        rateLimitInfo.recordRequest(now);
        
        return true;
    }

    /**
     * 判断是否应该跳过频率限制
     *
     * @param requestPath 请求路径
     * @return 是否跳过
     */
    private boolean shouldSkipRateLimit(String requestPath) {
        return requestPath.startsWith("/actuator/health") ||
               requestPath.startsWith("/actuator/info") ||
               requestPath.startsWith("/swagger-ui/") ||
               requestPath.startsWith("/v3/api-docs") ||
               requestPath.startsWith("/swagger-resources/") ||
               requestPath.startsWith("/webjars/") ||
               requestPath.equals("/swagger-ui.html") ||
               requestPath.equals("/v3/api-docs") ||
               requestPath.equals("/") ||
               requestPath.equals("/favicon.ico") ||
               requestPath.equals("/error");
    }

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 发送频率限制超出响应
     * 
     * @param response HTTP响应
     * @param clientIp 客户端IP
     */
    private void sendRateLimitExceededResponse(HttpServletResponse response, String clientIp) throws IOException {
        response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        // 添加频率限制头部
        response.setHeader("X-RateLimit-Limit-Minute", String.valueOf(securityProperties.getRateLimit().getRequestsPerMinute()));
        response.setHeader("X-RateLimit-Limit-Hour", String.valueOf(securityProperties.getRateLimit().getRequestsPerHour()));
        response.setHeader("X-RateLimit-Limit-Day", String.valueOf(securityProperties.getRateLimit().getRequestsPerDay()));
        response.setHeader("Retry-After", "60"); // 建议60秒后重试

        ApiResponse<Object> errorResponse = ApiResponse.error(429, 
            "请求频率超出限制，请稍后再试");
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    /**
     * 频率限制信息
     */
    private static class RateLimitInfo {
        private final Map<LocalDateTime, Integer> requestCounts = new ConcurrentHashMap<>();

        public void recordRequest(LocalDateTime timestamp) {
            // 记录到分钟级别
            LocalDateTime minuteKey = timestamp.truncatedTo(ChronoUnit.MINUTES);
            requestCounts.merge(minuteKey, 1, Integer::sum);
        }

        public int getRequestsInLastMinute(LocalDateTime now) {
            LocalDateTime oneMinuteAgo = now.minus(1, ChronoUnit.MINUTES);
            return requestCounts.entrySet().stream()
                .filter(entry -> entry.getKey().isAfter(oneMinuteAgo))
                .mapToInt(Map.Entry::getValue)
                .sum();
        }

        public int getRequestsInLastHour(LocalDateTime now) {
            LocalDateTime oneHourAgo = now.minus(1, ChronoUnit.HOURS);
            return requestCounts.entrySet().stream()
                .filter(entry -> entry.getKey().isAfter(oneHourAgo))
                .mapToInt(Map.Entry::getValue)
                .sum();
        }

        public int getRequestsInLastDay(LocalDateTime now) {
            LocalDateTime oneDayAgo = now.minus(1, ChronoUnit.DAYS);
            return requestCounts.entrySet().stream()
                .filter(entry -> entry.getKey().isAfter(oneDayAgo))
                .mapToInt(Map.Entry::getValue)
                .sum();
        }

        public void cleanupExpiredCounters(LocalDateTime now) {
            LocalDateTime cutoff = now.minus(1, ChronoUnit.DAYS);
            requestCounts.entrySet().removeIf(entry -> entry.getKey().isBefore(cutoff));
        }
    }
}
