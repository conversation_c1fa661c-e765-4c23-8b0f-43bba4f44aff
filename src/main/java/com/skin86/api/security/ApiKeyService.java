package com.skin86.api.security;

import com.skin86.api.config.SecurityConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * API Key认证服务
 * 负责API Key的验证、管理和审计日志记录
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Service
public class ApiKeyService {

    private static final Logger logger = LoggerFactory.getLogger(ApiKeyService.class);
    private static final Logger auditLogger = LoggerFactory.getLogger("AUDIT");

    private final SecurityConfig.SecurityProperties securityProperties;
    private final Map<String, ApiKeyInfo> apiKeyUsage = new ConcurrentHashMap<>();

    public ApiKeyService(SecurityConfig.SecurityProperties securityProperties) {
        this.securityProperties = securityProperties;
    }

    /**
     * 验证API Key是否有效
     * 
     * @param apiKey API密钥
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 是否有效
     */
    public boolean validateApiKey(String apiKey, String clientIp, String userAgent) {
        if (!securityProperties.getApiKey().isEnabled()) {
            return true; // 如果API Key认证被禁用，则允许访问
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            auditLogger.warn("API访问被拒绝 - 缺少API Key, IP: {}, UserAgent: {}", clientIp, userAgent);
            return false;
        }

        boolean isValid = securityProperties.getApiKey().getValidKeys().contains(apiKey);
        
        if (isValid) {
            // 记录成功的API Key使用
            recordApiKeyUsage(apiKey, clientIp, userAgent, true);
            auditLogger.info("API访问成功 - API Key: {}, IP: {}, UserAgent: {}", 
                maskApiKey(apiKey), clientIp, userAgent);
        } else {
            // 记录失败的API Key使用
            auditLogger.warn("API访问被拒绝 - 无效API Key: {}, IP: {}, UserAgent: {}", 
                maskApiKey(apiKey), clientIp, userAgent);
        }

        return isValid;
    }

    /**
     * 检查IP是否在白名单中
     * 
     * @param clientIp 客户端IP
     * @return 是否允许访问
     */
    public boolean isIpAllowed(String clientIp) {
        if (!securityProperties.getIpWhitelist().isEnabled()) {
            return true; // 如果IP白名单被禁用，则允许所有IP
        }

        boolean isAllowed = securityProperties.getIpWhitelist().getAllowedIps().contains(clientIp);
        
        if (!isAllowed) {
            auditLogger.warn("IP访问被拒绝 - IP不在白名单中: {}", clientIp);
        }

        return isAllowed;
    }

    /**
     * 记录API Key使用情况
     * 
     * @param apiKey API密钥
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param success 是否成功
     */
    private void recordApiKeyUsage(String apiKey, String clientIp, String userAgent, boolean success) {
        ApiKeyInfo info = apiKeyUsage.computeIfAbsent(apiKey, k -> new ApiKeyInfo());
        info.recordUsage(clientIp, userAgent, success);
    }

    /**
     * 获取API Key使用统计
     * 
     * @return 使用统计信息
     */
    public Map<String, ApiKeyInfo> getApiKeyUsageStats() {
        return Map.copyOf(apiKeyUsage);
    }

    /**
     * 掩码API Key用于日志记录
     * 
     * @param apiKey 原始API Key
     * @return 掩码后的API Key
     */
    private String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }

    /**
     * API Key使用信息
     */
    public static class ApiKeyInfo {
        private long totalRequests = 0;
        private long successfulRequests = 0;
        private long failedRequests = 0;
        private LocalDateTime firstUsed;
        private LocalDateTime lastUsed;
        private String lastClientIp;
        private String lastUserAgent;

        public synchronized void recordUsage(String clientIp, String userAgent, boolean success) {
            totalRequests++;
            if (success) {
                successfulRequests++;
            } else {
                failedRequests++;
            }
            
            LocalDateTime now = LocalDateTime.now();
            if (firstUsed == null) {
                firstUsed = now;
            }
            lastUsed = now;
            lastClientIp = clientIp;
            lastUserAgent = userAgent;
        }

        // Getters
        public long getTotalRequests() { return totalRequests; }
        public long getSuccessfulRequests() { return successfulRequests; }
        public long getFailedRequests() { return failedRequests; }
        public LocalDateTime getFirstUsed() { return firstUsed; }
        public LocalDateTime getLastUsed() { return lastUsed; }
        public String getLastClientIp() { return lastClientIp; }
        public String getLastUserAgent() { return lastUserAgent; }
    }
}
