package com.skin86.api.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.skin86.api.config.SecurityConfig;
import com.skin86.api.dto.ApiResponse;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * API Key认证过滤器
 * 验证请求中的API Key并设置安全上下文
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class ApiKeyAuthenticationFilter extends OncePerRequestFilter {

    private final ApiKeyService apiKeyService;
    private final SecurityConfig.SecurityProperties securityProperties;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public ApiKeyAuthenticationFilter(ApiKeyService apiKeyService, 
                                    SecurityConfig.SecurityProperties securityProperties) {
        this.apiKeyService = apiKeyService;
        this.securityProperties = securityProperties;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {

        String requestPath = request.getRequestURI();
        
        // 跳过不需要认证的路径
        if (shouldSkipAuthentication(requestPath)) {
            filterChain.doFilter(request, response);
            return;
        }

        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");

        // 检查IP白名单
        if (!apiKeyService.isIpAllowed(clientIp)) {
            sendErrorResponse(response, HttpStatus.FORBIDDEN, 
                "IP地址不在允许的白名单中", "IP_NOT_ALLOWED");
            return;
        }

        // 获取API Key
        String apiKey = request.getHeader(securityProperties.getApiKey().getHeaderName());
        
        // 验证API Key
        if (!apiKeyService.validateApiKey(apiKey, clientIp, userAgent)) {
            sendErrorResponse(response, HttpStatus.UNAUTHORIZED, 
                "无效的API Key", "INVALID_API_KEY");
            return;
        }

        // 设置认证信息
        UsernamePasswordAuthenticationToken authentication = 
            new UsernamePasswordAuthenticationToken("api-user", null, Collections.emptyList());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        filterChain.doFilter(request, response);
    }

    /**
     * 判断是否应该跳过认证
     *
     * @param requestPath 请求路径
     * @return 是否跳过
     */
    private boolean shouldSkipAuthentication(String requestPath) {
        return requestPath.startsWith("/actuator/") ||
               requestPath.startsWith("/swagger-ui/") ||
               requestPath.startsWith("/v3/api-docs") ||
               requestPath.startsWith("/swagger-resources/") ||
               requestPath.startsWith("/webjars/") ||
               requestPath.equals("/swagger-ui.html") ||
               requestPath.equals("/v3/api-docs") ||
               requestPath.equals("/") ||
               requestPath.equals("/favicon.ico") ||
               requestPath.equals("/error");
    }

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 发送错误响应
     * 
     * @param response HTTP响应
     * @param status HTTP状态码
     * @param message 错误消息
     * @param errorCode 错误代码
     */
    private void sendErrorResponse(HttpServletResponse response, 
                                 HttpStatus status, 
                                 String message, 
                                 String errorCode) throws IOException {
        response.setStatus(status.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        ApiResponse<Object> errorResponse = ApiResponse.error(status.value(), message);
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
}
