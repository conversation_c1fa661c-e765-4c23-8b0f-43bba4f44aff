package com.skin86.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Skin86 API数据获取系统主启动类
 * 基于Spring Boot 3.x和Java 21构建的高性能数据采集系统
 * 支持多平台CS2饰品数据的自动化获取、存储和管理
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since Java 21
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@ConfigurationPropertiesScan
public class Application {

    public static void main(String[] args) {
        // 启用虚拟线程支持
        System.setProperty("spring.threads.virtual.enabled", "true");
        
        SpringApplication.run(Application.class, args);
    }
}
