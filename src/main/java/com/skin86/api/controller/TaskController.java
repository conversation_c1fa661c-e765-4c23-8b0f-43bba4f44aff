package com.skin86.api.controller;

import com.skin86.api.dto.ApiResponse;
import com.skin86.api.scheduler.CheckpointManager;
import com.skin86.api.scheduler.TaskCheckpoint;
import com.skin86.api.scheduler.TaskScheduler;
import com.skin86.api.service.DataStorageService;
import com.skin86.api.service.MonitoringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 任务控制器
 * 提供任务管理和监控的REST API接口
 * 支持手动执行任务、查看状态、管理断点等功能
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@RestController
@RequestMapping("/api/v1/tasks")
@Tag(name = "任务管理", description = "数据获取任务的管理和监控接口")
@SecurityRequirement(name = "ApiKeyAuth")
public class TaskController {

    private final TaskScheduler taskScheduler;
    private final CheckpointManager checkpointManager;
    private final DataStorageService dataStorageService;
    private final MonitoringService monitoringService;

    public TaskController(TaskScheduler taskScheduler,
                         CheckpointManager checkpointManager,
                         DataStorageService dataStorageService,
                         MonitoringService monitoringService) {
        this.taskScheduler = taskScheduler;
        this.checkpointManager = checkpointManager;
        this.dataStorageService = dataStorageService;
        this.monitoringService = monitoringService;
    }

    /**
     * 获取任务状态
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Object>> getTaskStatus() {
        var stats = taskScheduler.getTaskStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 手动执行商品数据获取
     */
    @PostMapping("/goods/fetch")
    public ResponseEntity<ApiResponse<String>> fetchGoods(
            @RequestParam String platform,
            @RequestParam(required = false) Integer startPage,
            @RequestParam(required = false) Integer endPage) {
        
        if (taskScheduler.isRunning()) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(400, "任务正在运行中，请稍后再试"));
        }

        CompletableFuture<String> result = taskScheduler.executeManualGoodsFetch(platform, startPage, endPage);
        return ResponseEntity.ok(ApiResponse.success("任务已启动，请查看日志获取执行结果"));
    }

    /**
     * 手动执行价格数据获取
     */
    @PostMapping("/prices/fetch")
    public ResponseEntity<ApiResponse<String>> fetchPrices(
            @RequestParam String platform,
            @RequestParam(required = false) String date) {
        
        if (taskScheduler.isRunning()) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(400, "任务正在运行中，请稍后再试"));
        }

        if (date == null) {
            date = java.time.LocalDate.now().toString();
        }

        CompletableFuture<String> result = taskScheduler.executeManualPriceFetch(platform, date);
        
        return ResponseEntity.ok(ApiResponse.success("价格获取任务已启动"));
    }

    /**
     * 停止当前任务
     */
    @PostMapping("/stop")
    public ResponseEntity<ApiResponse<String>> stopTask() {
        taskScheduler.stopCurrentTask();
        return ResponseEntity.ok(ApiResponse.success("停止请求已发送"));
    }

    /**
     * 获取断点信息
     */
    @GetMapping("/checkpoints")
    public ResponseEntity<ApiResponse<Map<String, TaskCheckpoint>>> getCheckpoints() {
        var checkpoints = checkpointManager.getAllCheckpoints();
        return ResponseEntity.ok(ApiResponse.success(checkpoints));
    }

    /**
     * 获取指定任务的断点
     */
    @GetMapping("/checkpoints/{taskKey}")
    public ResponseEntity<ApiResponse<TaskCheckpoint>> getCheckpoint(@PathVariable String taskKey) {
        TaskCheckpoint checkpoint = checkpointManager.getCheckpoint(taskKey);
        if (checkpoint == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(ApiResponse.success(checkpoint));
    }

    /**
     * 删除断点
     */
    @DeleteMapping("/checkpoints/{taskKey}")
    public ResponseEntity<ApiResponse<String>> deleteCheckpoint(@PathVariable String taskKey) {
        checkpointManager.removeCheckpoint(taskKey);
        return ResponseEntity.ok(ApiResponse.success("断点已删除"));
    }

    /**
     * 清除所有断点
     */
    @DeleteMapping("/checkpoints")
    public ResponseEntity<ApiResponse<String>> clearAllCheckpoints() {
        checkpointManager.clearAllCheckpoints();
        return ResponseEntity.ok(ApiResponse.success("所有断点已清除"));
    }

    /**
     * 获取断点统计
     */
    @GetMapping("/checkpoints/stats")
    public ResponseEntity<ApiResponse<Object>> getCheckpointStats() {
        var stats = checkpointManager.getCheckpointStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 清理过期断点
     */
    @PostMapping("/checkpoints/cleanup")
    public ResponseEntity<ApiResponse<String>> cleanupCheckpoints(
            @RequestParam(defaultValue = "7") int daysToKeep) {
        int removedCount = checkpointManager.cleanupExpiredCheckpoints(daysToKeep);
        return ResponseEntity.ok(ApiResponse.success("清理完成，删除" + removedCount + "个过期断点"));
    }

    /**
     * 获取数据库统计
     */
    @GetMapping("/database/stats")
    public ResponseEntity<ApiResponse<Object>> getDatabaseStats() {
        var stats = dataStorageService.getDatabaseStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 获取执行统计
     */
    @GetMapping("/execution/stats")
    public ResponseEntity<ApiResponse<Object>> getExecutionStats() {
        var stats = monitoringService.getExecutionStats();
        return ResponseEntity.ok(ApiResponse.success(stats));
    }

    /**
     * 重置统计信息
     */
    @PostMapping("/execution/stats/reset")
    public ResponseEntity<ApiResponse<String>> resetExecutionStats() {
        monitoringService.resetStats();
        return ResponseEntity.ok(ApiResponse.success("统计信息已重置"));
    }

    /**
     * 清理平台数据
     */
    @DeleteMapping("/database/{platform}")
    public ResponseEntity<ApiResponse<Object>> cleanPlatformData(@PathVariable String platform) {
        if (taskScheduler.isRunning()) {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(400, "任务正在运行中，无法清理数据"));
        }

        var result = dataStorageService.cleanPlatformData(platform);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * 获取平台商品数量
     */
    @GetMapping("/database/{platform}/count")
    public ResponseEntity<ApiResponse<Long>> getPlatformGoodsCount(@PathVariable String platform) {
        long count = dataStorageService.countGoodsByPlatform(platform);
        return ResponseEntity.ok(ApiResponse.success(count));
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Object>> healthCheck() {
        Map<String, Object> health = Map.of(
            "status", "UP",
            "timestamp", java.time.LocalDateTime.now(),
            "isRunning", taskScheduler.isRunning(),
            "memoryUsage", Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory(),
            "activeThreads", Thread.activeCount()
        );
        return ResponseEntity.ok(ApiResponse.success(health));
    }
}
