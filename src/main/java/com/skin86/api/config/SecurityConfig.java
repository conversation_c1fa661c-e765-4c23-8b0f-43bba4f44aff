package com.skin86.api.config;

import com.skin86.api.security.ApiKeyAuthenticationFilter;
import com.skin86.api.security.ApiKeyService;
import com.skin86.api.security.RateLimitingFilter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.List;

/**
 * 安全配置类
 * 实现API Key认证、请求频率限制、IP白名单等安全机制
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Configuration
@EnableWebSecurity
@EnableConfigurationProperties(SecurityConfig.SecurityProperties.class)
public class SecurityConfig {

    private final ApiKeyService apiKeyService;
    private final SecurityProperties securityProperties;

    public SecurityConfig(ApiKeyService apiKeyService, SecurityProperties securityProperties) {
        this.apiKeyService = apiKeyService;
        this.securityProperties = securityProperties;
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                // 公开端点 - 健康检查
                .requestMatchers("/actuator/health", "/actuator/info").permitAll()
                // 公开端点 - API文档
                .requestMatchers("/swagger-ui/**", "/swagger-ui.html").permitAll()
                .requestMatchers("/v3/api-docs/**", "/v3/api-docs").permitAll()
                .requestMatchers("/swagger-resources/**").permitAll()
                .requestMatchers("/webjars/**").permitAll()
                // 公开端点 - 根路径和静态资源
                .requestMatchers("/", "/favicon.ico", "/error").permitAll()
                // API端点需要认证
                .requestMatchers("/api/**").authenticated()
                .anyRequest().permitAll()
            )
            // 添加API Key认证过滤器
            .addFilterBefore(new ApiKeyAuthenticationFilter(apiKeyService, securityProperties), 
                           UsernamePasswordAuthenticationFilter.class)
            // 添加频率限制过滤器
            .addFilterBefore(new RateLimitingFilter(securityProperties), 
                           ApiKeyAuthenticationFilter.class);

        return http.build();
    }

    /**
     * 安全配置属性
     */
    @ConfigurationProperties(prefix = "skin86.security")
    public static class SecurityProperties {
        
        /**
         * API Key配置
         */
        private ApiKeyConfig apiKey = new ApiKeyConfig();
        
        /**
         * 频率限制配置
         */
        private RateLimitConfig rateLimit = new RateLimitConfig();
        
        /**
         * IP白名单配置
         */
        private IpWhitelistConfig ipWhitelist = new IpWhitelistConfig();

        // Getters and Setters
        public ApiKeyConfig getApiKey() { return apiKey; }
        public void setApiKey(ApiKeyConfig apiKey) { this.apiKey = apiKey; }
        
        public RateLimitConfig getRateLimit() { return rateLimit; }
        public void setRateLimit(RateLimitConfig rateLimit) { this.rateLimit = rateLimit; }
        
        public IpWhitelistConfig getIpWhitelist() { return ipWhitelist; }
        public void setIpWhitelist(IpWhitelistConfig ipWhitelist) { this.ipWhitelist = ipWhitelist; }

        public static class ApiKeyConfig {
            private boolean enabled = true;
            private String headerName = "X-API-Key";
            private List<String> validKeys = List.of("sk-test-12345", "sk-prod-67890");
            
            // Getters and Setters
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public String getHeaderName() { return headerName; }
            public void setHeaderName(String headerName) { this.headerName = headerName; }
            
            public List<String> getValidKeys() { return validKeys; }
            public void setValidKeys(List<String> validKeys) { this.validKeys = validKeys; }
        }

        public static class RateLimitConfig {
            private boolean enabled = true;
            private int requestsPerMinute = 60;
            private int requestsPerHour = 1000;
            private int requestsPerDay = 10000;
            
            // Getters and Setters
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public int getRequestsPerMinute() { return requestsPerMinute; }
            public void setRequestsPerMinute(int requestsPerMinute) { this.requestsPerMinute = requestsPerMinute; }
            
            public int getRequestsPerHour() { return requestsPerHour; }
            public void setRequestsPerHour(int requestsPerHour) { this.requestsPerHour = requestsPerHour; }
            
            public int getRequestsPerDay() { return requestsPerDay; }
            public void setRequestsPerDay(int requestsPerDay) { this.requestsPerDay = requestsPerDay; }
        }

        public static class IpWhitelistConfig {
            private boolean enabled = false;
            private List<String> allowedIps = List.of("127.0.0.1", "::1");
            
            // Getters and Setters
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public List<String> getAllowedIps() { return allowedIps; }
            public void setAllowedIps(List<String> allowedIps) { this.allowedIps = allowedIps; }
        }
    }
}
