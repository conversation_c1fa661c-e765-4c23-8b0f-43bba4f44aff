package com.skin86.api.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI/Swagger配置
 * 提供完整的API文档和交互式测试界面
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
            .info(new Info()
                .title("Skin86 API数据获取系统")
                .description("""
                    # Skin86 API数据获取系统
                    
                    这是一个高性能的CS2饰品数据获取和管理系统，提供以下功能：
                    
                    ## 主要功能
                    - 🚀 **高性能数据获取**：支持多平台并发数据采集
                    - 📊 **实时数据监控**：提供详细的系统状态和统计信息
                    - 🔄 **断点续传**：支持任务中断后的断点恢复
                    - 🛡️ **安全认证**：API Key认证和请求频率限制
                    - 📈 **性能优化**：基于Java 21虚拟线程的高并发处理
                    
                    ## 认证方式
                    所有API端点都需要在请求头中包含有效的API Key：
                    ```
                    X-API-Key: your-api-key-here
                    ```
                    
                    ## 频率限制
                    - 每分钟最多60次请求
                    - 每小时最多1000次请求
                    - 每天最多10000次请求
                    
                    ## 错误码说明
                    - `200`: 请求成功
                    - `400`: 请求参数错误
                    - `401`: API Key无效或缺失
                    - `403`: IP地址不在白名单中
                    - `429`: 请求频率超出限制
                    - `500`: 服务器内部错误
                    """)
                .version("1.0.0")
                .contact(new Contact()
                    .name("Skin86 Team")
                    .email("<EMAIL>")
                    .url("https://skin86.com"))
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT")))
            .servers(List.of(
                new Server()
                    .url("http://localhost:8989")
                    .description("开发环境"),
                new Server()
                    .url("https://api.skin86.com")
                    .description("生产环境")))
            .addSecurityItem(new SecurityRequirement().addList("ApiKeyAuth"))
            .components(new io.swagger.v3.oas.models.Components()
                .addSecuritySchemes("ApiKeyAuth", new SecurityScheme()
                    .type(SecurityScheme.Type.APIKEY)
                    .in(SecurityScheme.In.HEADER)
                    .name("X-API-Key")
                    .description("API密钥认证，请在请求头中包含有效的API Key")));
    }
}
