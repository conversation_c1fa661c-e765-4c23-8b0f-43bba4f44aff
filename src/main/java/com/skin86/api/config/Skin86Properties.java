package com.skin86.api.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.util.List;

/**
 * Skin86 API系统配置属性
 * 
 * 使用Java 21 Record类简化配置管理
 * 支持配置验证和类型安全的属性绑定
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@ConfigurationProperties(prefix = "skin86")
@Validated
public record Skin86Properties(
    @Valid @NotNull ApiConfig api,
    @Valid @NotNull DatabaseConfig database,
    @Valid @NotNull PerformanceConfig performance,
    @Valid @NotNull SchedulerConfig scheduler,
    @Valid @NotNull PlatformConfig platform,
    @Valid @NotNull MonitoringConfig monitoring,
    @Valid @NotNull FormatConfig format
) {

    /**
     * API相关配置
     */
    public record ApiConfig(
        @NotBlank String appId,
        @NotBlank String appSecret,
        @NotBlank String baseUrl,
        @NotNull Duration timeout
    ) {
        public ApiConfig {
            // 默认值处理
            if (timeout == null) {
                timeout = Duration.ofSeconds(30);
            }
        }
    }

    /**
     * 数据库相关配置
     */
    public record DatabaseConfig(
        @NotBlank String url,
        @NotBlank String username,
        @NotBlank String password,
        @Min(1) @Max(100) int connectionPoolSize,
        @NotNull Duration connectionTimeout,
        @NotNull Duration transactionTimeout
    ) {
        public DatabaseConfig {
            // 默认值处理
            if (connectionTimeout == null) {
                connectionTimeout = Duration.ofSeconds(60);
            }
            if (transactionTimeout == null) {
                transactionTimeout = Duration.ofMinutes(5);
            }
        }
    }

    /**
     * 性能相关配置
     */
    public record PerformanceConfig(
        @Min(1) @Max(20) int maxConcurrency,
        @NotNull Duration delayMs,
        @Min(1) @Max(10) int retryTimes,
        @NotNull Duration retryDelay,
        @Min(1) @Max(10000) int goodsBatchSize,
        @Min(1) @Max(1000) int priceBatchSize,
        @Min(1) @Max(10000) int dbBatchSize
    ) {
        public PerformanceConfig {
            // 默认值处理
            if (delayMs == null) {
                delayMs = Duration.ofMillis(1000);
            }
            if (retryDelay == null) {
                retryDelay = Duration.ofSeconds(5);
            }
        }
    }

    /**
     * 调度器相关配置
     */
    public record SchedulerConfig(
        @Min(1) @Max(168) int defaultIntervalHours,
        boolean enableCheckpoint,
        @Min(1) @Max(60) int checkpointIntervalMinutes,
        @NotBlank String logFile,
        @NotBlank String pidFile,
        @NotBlank String checkpointFile,
        @Min(1) @Max(168) int goodsFullUpdateHours,
        @Min(1) @Max(72) int goodsFreshnessHours,
        @Min(1) @Max(24) int checkpointExpireHours
    ) {
    }

    /**
     * 平台相关配置
     */
    public record PlatformConfig(
        @NotNull List<String> supported,
        @NotNull List<String> defaultPlatforms,
        @Valid @NotNull PlatformSpecific buff,
        @Valid @NotNull PlatformSpecific igxe,
        @Valid @NotNull PlatformSpecific steam,
        @Valid @NotNull PlatformSpecific yp
    ) {
        
        /**
         * 平台特定配置
         */
        public record PlatformSpecific(
            @Min(1) @Max(10) int maxConcurrency,
            @NotNull Duration delayMs,
            @Min(1) @Max(1000) int batchSize
        ) {
            public PlatformSpecific {
                if (delayMs == null) {
                    delayMs = Duration.ofMillis(1000);
                }
            }
        }
    }

    /**
     * 监控相关配置
     */
    public record MonitoringConfig(
        boolean enableStats,
        @NotNull Duration statsInterval,
        boolean enableMemoryMonitor,
        @DecimalMin("0.1") @DecimalMax("1.0") double memoryThreshold,
        @NotBlank String logLevel,
        boolean enableFileLogging,
        @NotBlank String logDir,
        @NotBlank String maxFileSize,
        @Min(1) @Max(100) int maxFiles
    ) {
        public MonitoringConfig {
            if (statsInterval == null) {
                statsInterval = Duration.ofSeconds(10);
            }
        }
    }

    /**
     * 格式相关配置
     */
    public record FormatConfig(
        @NotBlank String datePattern
    ) {
        public FormatConfig {
            if (datePattern == null || datePattern.isBlank()) {
                datePattern = "yyyy-MM-dd";
            }
        }
    }
}
