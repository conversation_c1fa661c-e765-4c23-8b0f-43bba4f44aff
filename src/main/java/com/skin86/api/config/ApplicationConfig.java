package com.skin86.api.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.Duration;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 应用程序配置类
 * 
 * 配置HTTP客户端、线程池、异步执行器等基础组件
 * 利用Java 21的虚拟线程提升并发性能
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Configuration
@EnableConfigurationProperties(Skin86Properties.class)
public class ApplicationConfig implements AsyncConfigurer {

    private final Skin86Properties properties;

    public ApplicationConfig(Skin86Properties properties) {
        this.properties = properties;
    }

    /**
     * 配置ObjectMapper
     *
     * @return 配置好的ObjectMapper实例
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    /**
     * 配置OkHttp客户端 - 性能优化版
     *
     * @return 配置好的OkHttpClient实例
     */
    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
            .connectTimeout(Duration.ofSeconds(10))           // 连接超时：30s → 10s
            .readTimeout(Duration.ofSeconds(20))              // 读取超时：30s → 20s
            .writeTimeout(Duration.ofSeconds(10))             // 写入超时：30s → 10s
            .callTimeout(Duration.ofSeconds(30))              // 总调用超时
            .retryOnConnectionFailure(true)
            .connectionPool(new okhttp3.ConnectionPool(
                20,                                           // 最大空闲连接数：20
                5,                                            // 连接保活时间：5分钟
                java.util.concurrent.TimeUnit.MINUTES
            ))
            .dispatcher(createOptimizedDispatcher())
            .build();
    }

    /**
     * 创建优化的OkHttp Dispatcher
     *
     * @return 配置好的Dispatcher
     */
    private okhttp3.Dispatcher createOptimizedDispatcher() {
        okhttp3.Dispatcher dispatcher = new okhttp3.Dispatcher();
        dispatcher.setMaxRequests(50);                        // 最大并发请求：50
        dispatcher.setMaxRequestsPerHost(20);                 // 每个主机最大并发：20
        return dispatcher;
    }

    /**
     * 配置虚拟线程执行器
     * 
     * 使用Java 21的虚拟线程特性，支持大量并发任务
     * 
     * @return 虚拟线程执行器
     */
    @Bean("virtualThreadExecutor")
    public Executor virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }

    /**
     * 配置数据获取专用线程池 - 性能优化版
     *
     * @return 数据获取线程池
     */
    @Bean("dataFetchExecutor")
    public Executor dataFetchExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.performance().maxConcurrency() * 2);    // 核心线程数翻倍
        executor.setMaxPoolSize(properties.performance().maxConcurrency() * 4);     // 最大线程数增加
        executor.setQueueCapacity(500);                                             // 增加队列容量：100 → 500
        executor.setThreadNamePrefix("DataFetch-");
        executor.setKeepAliveSeconds(300);                                          // 线程保活时间：5分钟
        executor.setAllowCoreThreadTimeOut(true);                                  // 允许核心线程超时
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 配置数据处理专用线程池 - 性能优化版
     *
     * @return 数据处理线程池
     */
    @Bean("dataProcessExecutor")
    public Executor dataProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);                                               // 核心线程数：4 → 8
        executor.setMaxPoolSize(16);                                               // 最大线程数：8 → 16
        executor.setQueueCapacity(1000);                                           // 队列容量：200 → 1000
        executor.setThreadNamePrefix("DataProcess-");
        executor.setKeepAliveSeconds(300);                                          // 线程保活时间：5分钟
        executor.setAllowCoreThreadTimeOut(true);                                  // 允许核心线程超时
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    /**
     * 默认异步执行器配置
     * 
     * @return 默认异步执行器
     */
    @Override
    public Executor getAsyncExecutor() {
        return virtualThreadExecutor();
    }
}
