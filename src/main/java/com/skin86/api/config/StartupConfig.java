package com.skin86.api.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 启动时数据获取配置
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Component
@ConfigurationProperties(prefix = "skin86.scheduler.startup")
public class StartupConfig {
    
    /**
     * 是否启用启动时自动获取数据
     */
    private boolean enabled = true;
    
    /**
     * 启动延迟时间（秒）
     */
    private int delaySeconds = 5;
    
    /**
     * 启动时获取的平台列表
     */
    private List<String> platforms = List.of("yp");
    
    /**
     * 是否强制全量更新
     */
    private boolean forceFullUpdate = false;
    
    /**
     * 启动任务超时时间（分钟）
     */
    private int timeoutMinutes = 30;
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public int getDelaySeconds() {
        return delaySeconds;
    }
    
    public void setDelaySeconds(int delaySeconds) {
        this.delaySeconds = delaySeconds;
    }
    
    public List<String> getPlatforms() {
        return platforms;
    }
    
    public void setPlatforms(List<String> platforms) {
        this.platforms = platforms;
    }
    
    public boolean isForceFullUpdate() {
        return forceFullUpdate;
    }
    
    public void setForceFullUpdate(boolean forceFullUpdate) {
        this.forceFullUpdate = forceFullUpdate;
    }
    
    public int getTimeoutMinutes() {
        return timeoutMinutes;
    }
    
    public void setTimeoutMinutes(int timeoutMinutes) {
        this.timeoutMinutes = timeoutMinutes;
    }
    
    @Override
    public String toString() {
        return "StartupConfig{" +
                "enabled=" + enabled +
                ", delaySeconds=" + delaySeconds +
                ", platforms=" + platforms +
                ", forceFullUpdate=" + forceFullUpdate +
                ", timeoutMinutes=" + timeoutMinutes +
                '}';
    }
}
