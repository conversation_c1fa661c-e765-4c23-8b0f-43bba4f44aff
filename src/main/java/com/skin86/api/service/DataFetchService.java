package com.skin86.api.service;

import com.skin86.api.client.Skin86ApiClient;
import com.skin86.api.config.Skin86Properties;
import com.skin86.api.dto.ApiResponse;
import com.skin86.api.dto.request.BatchPricesRequest;
import com.skin86.api.dto.request.GoodsListRequest;
import com.skin86.api.dto.response.BatchPricesResponse;
import com.skin86.api.dto.response.GoodsListResponse;
import com.skin86.api.entity.SkinGoods;
import com.skin86.api.entity.PriceHistory;
import com.skin86.api.service.converter.DataConverter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * 数据获取服务
 *
 * 负责从Skin86 API获取数据并进行转换
 * 支持并发控制、重试机制
 * 注意：已移除数据验证，直接存储API返回的所有数据
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class DataFetchService {

    private static final Logger logger = LoggerFactory.getLogger(DataFetchService.class);

    private final Skin86ApiClient apiClient;
    private final DataConverter dataConverter;
    private final Skin86Properties properties;
    private final Semaphore concurrencyLimiter;
    private final DataStorageService dataStorageService; // 新增

    public DataFetchService(Skin86ApiClient apiClient,
                           DataConverter dataConverter,
                           Skin86Properties properties,
                           DataStorageService dataStorageService) { // 新增
        this.apiClient = apiClient;
        this.dataConverter = dataConverter;
        this.properties = properties;
        this.dataStorageService = dataStorageService; // 新增
        this.concurrencyLimiter = new Semaphore(properties.performance().maxConcurrency());
    }

    /**
     * 获取指定平台的商品数据
     * 
     * @param platform 平台名称
     * @param startPage 起始页码
     * @param endPage 结束页码（null表示获取所有）
     * @return 获取结果
     */
    @Async("dataFetchExecutor")
    public CompletableFuture<FetchResult<SkinGoods>> fetchGoodsData(
            String platform, Integer startPage, Integer endPage) {
        
        return CompletableFuture.supplyAsync(() -> {
            List<SkinGoods> allGoods = new ArrayList<>();
            List<String> errors = new ArrayList<>();
            int currentPage = startPage != null ? startPage : 1;
            int totalPages = (endPage != null) ? endPage : 0;
            int successCount = 0;
            int errorCount = 0;

            logger.info("开始获取{}平台商品数据，页码范围：{}-{}", platform, startPage, (endPage != null ? endPage : "所有页"));
            
            try {
                while (true) {
                    // 并发控制
                    concurrencyLimiter.acquire();
                    
                    try {
                        // 添加延迟避免API限流
                        if (currentPage > 1) {
                            Thread.sleep(properties.performance().delayMs().toMillis());
                        }
                        
                        // 调用API获取数据
                        String normalizedPlatform = normalizePlatformName(platform);
                        GoodsListRequest request = GoodsListRequest.of(
                            normalizedPlatform, currentPage, properties.performance().goodsBatchSize());
                        ApiResponse<GoodsListResponse> response = apiClient.getGoodsList(request).get();
                        
                        if (!response.isSuccess()) {
                            String error = String.format("API调用失败：%s - %s", response.code(), response.message());
                            logger.error(error);
                            errors.add(error);
                            errorCount++;
                            break;
                        }
                        
                        GoodsListResponse data = response.data();
                        if (data == null || data.list() == null || data.list().isEmpty()) {
                            logger.info("{}平台第{}页无数据，停止获取", platform, currentPage);
                            break;
                        }
                        
                        // 高性能数据转换 - 使用并行流处理
                        List<SkinGoods> pageGoods = data.list().parallelStream()
                            .map(item -> {
                                try {
                                    return dataConverter.convertToSkinGoods(item, platform);
                                } catch (Exception e) {
                                    logger.warn("转换商品数据失败：{}", e.getMessage());
                                    return null;
                                }
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                        allGoods.addAll(pageGoods);
                        successCount++;

                        // 异步保存商品数据，并在保存成功后提取价格历史
                        if (!pageGoods.isEmpty()) {
                            final String finalPlatform = platform;
                            final int finalCurrentPage = currentPage;
                            final List<SkinGoods> finalPageGoods = new ArrayList<>(pageGoods);

                            dataStorageService.saveSkinGoods(pageGoods)
                                .thenAccept(result -> {
                                    logger.info("{}平台第{}页商品数据保存完成：成功{}条",
                                        finalPlatform, finalCurrentPage, result.successCount());

                                    // 价格历史已在数据保存前自动处理
                                    // 遵循修正后的数据流程：
                                    // 1. 保存现有数据到price_history表
                                    // 2. 更新skin_goods表
                                    // 确保数据一致性：每次更新都有对应的历史记录
                                })
                                .exceptionally(ex -> {
                                    logger.error("{}平台第{}页数据保存失败", finalPlatform, finalCurrentPage, ex);
                                    return null;
                                });
                        }

                        // 设置总页数（首次获取时）
                        if (totalPages == 0 && data.total() != null && data.pageSize() != null && data.pageSize() > 0) {
                            totalPages = (int) Math.ceil((double) data.total() / data.pageSize());
                             logger.info("{}平台商品数据总页数：{}", platform, totalPages);
                        }
                        
                        logger.info("{}平台第{}页获取完成，本页{}条数据", platform, currentPage, pageGoods.size());
                        
                        // 检查是否达到结束条件
                        if (endPage != null && currentPage >= endPage) {
                            break;
                        }

                        if (totalPages > 0 && currentPage >= totalPages) {
                            logger.info("{}平台所有页面数据获取完成", platform);
                            break;
                        }
                        
                        if (data.list().size() < properties.performance().goodsBatchSize()) {
                            logger.info("{}平台数据获取完成，共{}页", platform, currentPage);
                            break;
                        }
                        
                        currentPage++;
                        
                    } finally {
                        concurrencyLimiter.release();
                    }
                }
                
            } catch (Exception e) {
                logger.error("获取{}平台商品数据异常", platform, e);
                errors.add("获取异常：" + e.getMessage());
            }
            
            FetchResult<SkinGoods> result = new FetchResult<>(
                allGoods, errors, successCount, errorCount, totalPages);
            
            logger.info("{}平台商品数据获取完成：成功{}条，错误{}个", 
                platform, allGoods.size(), errors.size());
            
            return result;
        });
    }

    /**
     * 获取指定平台的价格数据
     * 
     * @param platform 平台名称
     * @param goodsIds 商品ID列表
     * @param date 日期
     * @return 获取结果
     */
    @Async("dataFetchExecutor")
    public CompletableFuture<FetchResult<PriceHistory>> fetchPriceData(
            String platform, List<Integer> goodsIds, String date) {
        
        return CompletableFuture.supplyAsync(() -> {
            logger.info("开始获取{}平台价格数据，商品数量：{}", platform, goodsIds.size());
            
            List<PriceHistory> allPrices = new ArrayList<>();
            List<String> errors = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;
            
            // 分批处理商品ID
            int batchSize = properties.performance().priceBatchSize();
            for (int i = 0; i < goodsIds.size(); i += batchSize) {
                try {
                    // 并发控制
                    concurrencyLimiter.acquire();
                    
                    try {
                        // 添加延迟
                        if (i > 0) {
                            Thread.sleep(properties.performance().delayMs().toMillis());
                        }
                        
                        // 构建批次商品ID
                        List<Integer> batchIds = goodsIds.subList(i, Math.min(i + batchSize, goodsIds.size()));
                        String goodsIdsStr = String.join(",", batchIds.stream().map(String::valueOf).toList());
                        
                        // 调用API
                        BatchPricesRequest request = BatchPricesRequest.of(
                            platform, 1, batchSize, date, goodsIdsStr);
                        
                        ApiResponse<BatchPricesResponse> response = apiClient.getBatchPrices(request).get();
                        
                        if (!response.isSuccess()) {
                            String error = String.format("批次价格API调用失败：%s - %s", response.code(), response.message());
                            logger.error(error);
                            errors.add(error);
                            errorCount++;
                            continue;
                        }
                        
                        BatchPricesResponse data = response.data();
                        if (data != null && data.list() != null) {
                            // 数据转换和验证
                            for (var item : data.list()) {
                                try {
                                    PriceHistory priceHistory = dataConverter.convertToPriceHistory(item, platform);
                                    allPrices.add(priceHistory);

                                } catch (Exception e) {
                                    String error = "价格数据转换失败：" + e.getMessage();
                                    logger.warn(error, e);
                                    errors.add(error);
                                }
                            }
                        }
                        
                        successCount++;
                        logger.info("{}平台价格批次{}获取完成", platform, (i / batchSize) + 1);
                        
                    } finally {
                        concurrencyLimiter.release();
                    }
                    
                } catch (Exception e) {
                    logger.error("获取{}平台价格数据批次异常", platform, e);
                    errors.add("批次获取异常：" + e.getMessage());
                    errorCount++;
                }
            }
            
            FetchResult<PriceHistory> result = new FetchResult<>(
                allPrices, errors, successCount, errorCount, 0);
            
            logger.info("{}平台价格数据获取完成：成功{}条，错误{}个", 
                platform, allPrices.size(), errors.size());
            
            return result;
        });
    }

    /**
     * 数据获取结果
     */
    public record FetchResult<T>(
        List<T> data,
        List<String> errors,
        int successBatches,
        int errorBatches,
        int totalPages
    ) {
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
        
        public boolean isSuccess() {
            return errorBatches == 0;
        }
        
        public int getTotalRecords() {
            return data.size();
        }
    }

    /**
     * 从商品数据中提取价格历史记录并保存
     * 优化：避免N+1问题，批量处理价格历史记录
     *
     * @param goods 商品列表
     * @param platform 平台名称
     */
    private void extractAndSavePriceHistories(List<SkinGoods> goods, String platform) {
        try {
            logger.debug("开始从{}平台{}条商品数据中提取价格历史", platform, goods.size());

            // 批量提取价格历史，避免逐个处理导致的N+1问题
            List<PriceHistory> priceHistories = goods.stream()
                .filter(this::hasValidPriceInfo)  // 过滤有效价格数据
                .map(good -> {
                    try {
                        return dataConverter.extractPriceHistoryFromGoods(good, platform);
                    } catch (Exception e) {
                        logger.warn("提取商品{}的价格历史失败：{}", good.getGoodsId(), e.getMessage());
                        return null;
                    }
                })
                .filter(priceHistory -> priceHistory != null)  // 过滤转换失败的记录
                .toList();

            if (!priceHistories.isEmpty()) {
                logger.debug("{}平台提取到{}条有效价格历史记录", platform, priceHistories.size());

                // 使用优化的保存方法，避免N+1数据库操作
                dataStorageService.savePriceHistoryFromGoods(priceHistories)
                    .thenAccept(result -> {
                        logger.info("{}平台价格历史批量保存完成：成功{}条，失败{}条",
                            platform, result.successCount(), result.errorCount());
                    })
                    .exceptionally(ex -> {
                        logger.error("{}平台价格历史批量保存异常", platform, ex);
                        return null;
                    });
            } else {
                logger.debug("{}平台本批次无有效价格数据", platform);
            }

        } catch (Exception e) {
            logger.error("{}平台提取价格历史异常", platform, e);
        }
    }

    /**
     * 检查商品是否有有效的价格信息
     *
     * @param goods 商品实体
     * @return 是否有有效价格信息
     */
    private boolean hasValidPriceInfo(SkinGoods goods) {
        return goods.getSellMinPrice() != null && goods.getSellMinPrice().compareTo(BigDecimal.ZERO) > 0
            || goods.getBuyMaxPrice() != null && goods.getBuyMaxPrice().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 在商品数据保存成功后提取价格历史记录
     * 重新查询已保存的商品数据以获取数据库主键ID
     *
     * @param originalGoods 原始商品列表（没有数据库ID）
     * @param platform 平台名称
     */
    private void extractAndSavePriceHistoriesAfterSave(List<SkinGoods> originalGoods, String platform) {
        try {
            logger.debug("开始为{}平台{}条已保存商品提取价格历史", platform, originalGoods.size());

            // 根据goods_id和platform查询已保存的商品数据（包含数据库主键ID）
            List<Integer> goodsIds = originalGoods.stream()
                .map(SkinGoods::getGoodsId)
                .toList();

            // 查询已保存的商品数据
            List<SkinGoods> savedGoods = dataStorageService.findGoodsByGoodsIdsAndPlatform(goodsIds, platform);

            if (savedGoods.isEmpty()) {
                logger.warn("{}平台未找到已保存的商品数据，跳过价格历史提取", platform);
                return;
            }

            logger.debug("{}平台找到{}条已保存的商品数据，开始提取价格历史", platform, savedGoods.size());

            // 使用已保存的商品数据（包含数据库主键ID）提取价格历史
            extractAndSavePriceHistories(savedGoods, platform);

        } catch (Exception e) {
            logger.error("{}平台保存后价格历史提取异常", platform, e);
        }
    }

    /**
     * 标准化平台名称，确保符合API要求
     *
     * @param platform 原始平台名称
     * @return 标准化后的平台名称
     */
    private String normalizePlatformName(String platform) {
        if (platform == null) {
            return null;
        }

        // 转换为小写并处理特殊情况
        String normalized = platform.toLowerCase().trim();

        // 处理平台名称映射 - 转换为API要求的大写格式
        return switch (normalized) {
            case "yp" -> "YP";
            case "buff", "buff163" -> "BUFF";
            case "steam" -> "STEAM";
            case "igex", "igxe" -> "IGXE";
            default -> {
                logger.warn("未知的平台名称: {}，使用大写格式", platform);
                yield platform.toUpperCase();
            }
        };
    }
}
