package com.skin86.api.service;

import com.skin86.api.config.Skin86Properties;
import com.skin86.api.entity.PriceHistory;
import com.skin86.api.entity.SkinGoods;
import com.skin86.api.repository.PriceHistoryRepository;
import com.skin86.api.repository.SkinGoodsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 数据存储服务
 * 
 * 负责将数据批量存储到数据库
 * 支持事务管理、批量操作和错误处理
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class DataStorageService {

    private static final Logger logger = LoggerFactory.getLogger(DataStorageService.class);

    private final SkinGoodsRepository skinGoodsRepository;
    private final PriceHistoryRepository priceHistoryRepository;
    private final Skin86Properties properties;
    private final JdbcTemplate jdbcTemplate;

    public DataStorageService(SkinGoodsRepository skinGoodsRepository,
                             PriceHistoryRepository priceHistoryRepository,
                             Skin86Properties properties,
                             JdbcTemplate jdbcTemplate) {
        this.skinGoodsRepository = skinGoodsRepository;
        this.priceHistoryRepository = priceHistoryRepository;
        this.properties = properties;
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 批量保存饰品数据
     *
     * @param goodsList 饰品列表
     * @return 保存结果
     */
    @Async("dataProcessExecutor")
    @Transactional
    public CompletableFuture<StorageResult> saveSkinGoods(List<SkinGoods> goodsList) {
        // 直接在当前事务中执行，不使用CompletableFuture.supplyAsync
        try {
            if (goodsList == null || goodsList.isEmpty()) {
                logger.info("待保存的饰品数据列表为空，无需操作。");
                return CompletableFuture.completedFuture(new StorageResult(0, 0, new ArrayList<>()));
            }
            logger.info("开始批量保存饰品数据，数量：{}", goodsList.size());

            List<String> errors = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;

            try {
                // 第一步：保存当前数据到价格历史表（在更新之前）
                saveCurrentDataToPriceHistory(goodsList);

                // 第二步：批量保存/更新商品数据
                logger.info("开始批量保存饰品数据，数量：{}", goodsList.size());

                String sql = """
                    INSERT INTO skin_goods
                    (goods_id, platform_id, market_name, market_hash_name, sell_min_price,
                     sell_max_num, sell_valuation, buy_max_price, buy_max_num,
                     price_alter_percentage_7d, price_alter_value_7d, category_group_name,
                     rarity_color, icon_url, is_follow, redirect_url, exterior, rarity, platform,
                     created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ON DUPLICATE KEY UPDATE
                    platform_id = VALUES(platform_id),
                    market_name = VALUES(market_name),
                    market_hash_name = VALUES(market_hash_name),
                    sell_min_price = VALUES(sell_min_price),
                    sell_max_num = VALUES(sell_max_num),
                    sell_valuation = VALUES(sell_valuation),
                    buy_max_price = VALUES(buy_max_price),
                    buy_max_num = VALUES(buy_max_num),
                    price_alter_percentage_7d = VALUES(price_alter_percentage_7d),
                    price_alter_value_7d = VALUES(price_alter_value_7d),
                    category_group_name = VALUES(category_group_name),
                    rarity_color = VALUES(rarity_color),
                    icon_url = VALUES(icon_url),
                    is_follow = VALUES(is_follow),
                    redirect_url = VALUES(redirect_url),
                    exterior = VALUES(exterior),
                    rarity = VALUES(rarity),
                    updated_at = NOW()
                    """;

                int[] updateCounts;
                try {
                    updateCounts = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement ps, int i) throws SQLException {
                        SkinGoods goods = goodsList.get(i);
                        ps.setInt(1, goods.getGoodsId());
                        ps.setString(2, goods.getPlatformId());
                        ps.setString(3, goods.getMarketName());
                        ps.setString(4, goods.getMarketHashName());

                        // 安全设置BigDecimal值
                        if (goods.getSellMinPrice() != null) {
                            ps.setBigDecimal(5, goods.getSellMinPrice());
                        } else {
                            ps.setNull(5, java.sql.Types.DECIMAL);
                        }

                        // 安全设置Integer值
                        if (goods.getSellMaxNum() != null) {
                            ps.setInt(6, goods.getSellMaxNum());
                        } else {
                            ps.setNull(6, java.sql.Types.INTEGER);
                        }

                        if (goods.getSellValuation() != null) {
                            ps.setBigDecimal(7, goods.getSellValuation());
                        } else {
                            ps.setNull(7, java.sql.Types.DECIMAL);
                        }

                        if (goods.getBuyMaxPrice() != null) {
                            ps.setBigDecimal(8, goods.getBuyMaxPrice());
                        } else {
                            ps.setNull(8, java.sql.Types.DECIMAL);
                        }

                        if (goods.getBuyMaxNum() != null) {
                            ps.setInt(9, goods.getBuyMaxNum());
                        } else {
                            ps.setNull(9, java.sql.Types.INTEGER);
                        }

                        if (goods.getPriceAlterPercentage7d() != null) {
                            // 处理price_alter_percentage_7d的数据范围限制 (DECIMAL(10,4): -999999.9999 到 999999.9999)
                            BigDecimal priceAlterPercentage = goods.getPriceAlterPercentage7d();
                            if (priceAlterPercentage.compareTo(new BigDecimal("999999.9999")) > 0) {
                                priceAlterPercentage = new BigDecimal("999999.9999");
                            } else if (priceAlterPercentage.compareTo(new BigDecimal("-999999.9999")) < 0) {
                                priceAlterPercentage = new BigDecimal("-999999.9999");
                            }
                            ps.setBigDecimal(10, priceAlterPercentage);
                        } else {
                            ps.setNull(10, java.sql.Types.DECIMAL);
                        }

                        if (goods.getPriceAlterValue7d() != null) {
                            ps.setBigDecimal(11, goods.getPriceAlterValue7d());
                        } else {
                            ps.setNull(11, java.sql.Types.DECIMAL);
                        }

                        ps.setString(12, goods.getCategoryGroupName());
                        ps.setString(13, goods.getRarityColor());
                        ps.setString(14, goods.getIconUrl());

                        // 安全设置Boolean值
                        if (goods.getIsFollow() != null) {
                            ps.setBoolean(15, goods.getIsFollow());
                        } else {
                            ps.setNull(15, java.sql.Types.TINYINT);
                        }

                        ps.setString(16, goods.getRedirectUrl());
                        ps.setString(17, goods.getExterior());
                        ps.setString(18, goods.getRarity());
                        ps.setString(19, goods.getPlatform());
                    }

                    @Override
                    public int getBatchSize() {
                        return goodsList.size();
                    }
                });
                } catch (org.springframework.dao.DataAccessException batchException) {
                    logger.error("批量更新执行异常：{}", batchException.getMessage(), batchException);

                    // 检查是否是BatchUpdateException，获取更详细的错误信息
                    Throwable cause = batchException.getCause();
                    if (cause instanceof java.sql.BatchUpdateException) {
                        java.sql.BatchUpdateException bue = (java.sql.BatchUpdateException) cause;
                        logger.error("BatchUpdateException详细信息：");
                        logger.error("- 错误代码：{}", bue.getErrorCode());
                        logger.error("- SQL状态：{}", bue.getSQLState());
                        logger.error("- 更新计数：{}", java.util.Arrays.toString(bue.getUpdateCounts()));

                        // 获取下一个异常（通常包含更详细的错误信息）
                        SQLException nextException = bue.getNextException();
                        while (nextException != null) {
                            logger.error("- 下一个异常：{}", nextException.getMessage());
                            nextException = nextException.getNextException();
                        }
                    }
                    throw batchException;
                } catch (Exception otherException) {
                    logger.error("其他异常：{}", otherException.getMessage(), otherException);
                    throw otherException;
                }

                logger.debug("批量更新执行完成，返回结果数组长度：{}", updateCounts.length);

                for (int count : updateCounts) {
                    // MySQL批量更新返回值含义：
                    // 1: INSERT成功
                    // 2: UPDATE成功
                    // 0: 没有变化
                    // -2: Statement.EXECUTE_FAILED (但在ON DUPLICATE KEY UPDATE中可能是正常的)
                    // -3: Statement.SUCCESS_NO_INFO (成功但无具体信息)

                    if (count >= 0 || count == java.sql.Statement.SUCCESS_NO_INFO) {
                        successCount++;
                    } else if (count == java.sql.Statement.EXECUTE_FAILED) {
                        // 对于ON DUPLICATE KEY UPDATE，-2可能是正常的
                        // 我们认为这是成功的，除非有明确的异常
                        successCount++;
                        logger.debug("批量更新返回EXECUTE_FAILED(-2)，但在ON DUPLICATE KEY UPDATE中这可能是正常的");
                    } else {
                        errorCount++;
                    }
                }

            } catch (Exception e) {
                String error = String.format("饰品数据批量保存异常：%s", e.getMessage());
                logger.error(error, e);
                logger.error("异常堆栈信息：", e);

                // 打印第一个商品的详细信息用于调试
                if (!goodsList.isEmpty()) {
                    SkinGoods firstGoods = goodsList.get(0);
                    logger.error("第一个商品详细信息：goodsId={}, marketName={}, platform={}, sellMinPrice={}",
                        firstGoods.getGoodsId(), firstGoods.getMarketName(),
                        firstGoods.getPlatform(), firstGoods.getSellMinPrice());
                }

                errors.add(error);
                errorCount = goodsList.size() - successCount; // 无法精确到行，将剩余的都视为失败
            }

            StorageResult result = new StorageResult(successCount, errorCount, errors);
            logger.info("饰品数据保存完成：成功{}条，失败{}条", successCount, errorCount);

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            String error = String.format("饰品数据保存异常：%s", e.getMessage());
            logger.error(error, e);
            List<String> errors = List.of(error);
            return CompletableFuture.completedFuture(new StorageResult(0, goodsList.size(), errors));
        }
    }

    /**
     * 批量保存价格历史数据
     *
     * @param priceHistoryList 价格历史列表
     * @return 保存结果
     */
    @Async("dataProcessExecutor")
    @Transactional
    public CompletableFuture<StorageResult> savePriceHistory(List<PriceHistory> priceHistoryList) {
        return CompletableFuture.supplyAsync(() -> {
            logger.info("开始批量保存价格历史数据，数量：{}", priceHistoryList.size());
            
            List<String> errors = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;
            
            try {
                // 分批处理
                int batchSize = properties.performance().dbBatchSize();
                for (int i = 0; i < priceHistoryList.size(); i += batchSize) {
                    List<PriceHistory> batch = priceHistoryList.subList(i, Math.min(i + batchSize, priceHistoryList.size()));
                    try {
                        // 按平台分组处理，解决N+1问题
                        Map<String, List<PriceHistory>> platformGroups = batch.stream()
                            .collect(Collectors.groupingBy(PriceHistory::getDataSource));

                        // 为每个平台批量查询商品ID映射
                        Map<Integer, Integer> goodsIdToDbIdMap = new HashMap<>();
                        for (Map.Entry<String, List<PriceHistory>> entry : platformGroups.entrySet()) {
                            String platform = entry.getKey();
                            List<Integer> goodsIds = entry.getValue().stream()
                                .map(PriceHistory::getSkinGoodsId)
                                .distinct()
                                .toList();

                            // 批量查询该平台的商品ID映射
                            List<Object[]> mappings = skinGoodsRepository.findGoodsIdToDbIdMapping(goodsIds, platform);
                            mappings.forEach(row -> goodsIdToDbIdMap.put(
                                (Integer) row[0], // goods_id
                                (Integer) row[1]  // database id
                            ));
                        }

                        // 真正的批量插入价格数据
                        List<PriceHistory> validPriceHistories = new ArrayList<>();
                        for (PriceHistory priceHistory : batch) {
                            Integer dbSkinGoodsId = goodsIdToDbIdMap.get(priceHistory.getSkinGoodsId());

                            if (dbSkinGoodsId == null) {
                                logger.warn("商品不存在，跳过价格历史保存: goods_id={}, platform={}, name={}",
                                    priceHistory.getSkinGoodsId(), priceHistory.getDataSource(), priceHistory.getGoodsName());
                                errorCount++;
                                continue;
                            }

                            // 设置正确的数据库主键ID
                            priceHistory.setSkinGoodsId(dbSkinGoodsId);
                            validPriceHistories.add(priceHistory);
                        }

                        // 使用JdbcTemplate进行真正的批量插入
                        if (!validPriceHistories.isEmpty()) {
                            try {
                                int insertedCount = batchInsertPriceHistories(validPriceHistories);
                                successCount += insertedCount;
                                logger.info("批量保存价格历史成功：{}条", insertedCount);
                            } catch (Exception e) {
                                logger.error("批量保存价格历史失败", e);
                                errorCount += validPriceHistories.size();
                                errors.add("批量保存失败: " + e.getMessage());
                            }
                        }

                        
                        logger.debug("价格历史批次保存完成：{}/{}", (i / batchSize) + 1, 
                            (priceHistoryList.size() + batchSize - 1) / batchSize);
                        
                    } catch (Exception e) {
                        String error = String.format("价格历史批次保存异常：%s", e.getMessage());
                        logger.error(error, e);
                        errors.add(error);
                        errorCount += batch.size();
                    }
                }
                
            } catch (Exception e) {
                String error = String.format("价格历史数据保存异常：%s", e.getMessage());
                logger.error(error, e);
                errors.add(error);
            }
            
            StorageResult result = new StorageResult(successCount, errorCount, errors);
            logger.info("价格历史数据保存完成：成功{}条，失败{}条", successCount, errorCount);
            
            return result;
        });
    }

    /**
     * 批量保存从商品数据提取的价格历史记录
     * 优化版本：避免ID映射查询，直接使用已有的数据库主键ID
     *
     * @param priceHistoryList 价格历史列表（已包含正确的数据库主键ID）
     * @return 保存结果
     */
    @Async("dataProcessExecutor")
    @Transactional
    public CompletableFuture<StorageResult> savePriceHistoryFromGoods(List<PriceHistory> priceHistoryList) {
        return CompletableFuture.supplyAsync(() -> {
            if (priceHistoryList == null || priceHistoryList.isEmpty()) {
                logger.debug("待保存的价格历史数据列表为空，无需操作");
                return new StorageResult(0, 0, new ArrayList<>());
            }

            logger.info("开始批量保存从商品数据提取的价格历史，数量：{}", priceHistoryList.size());

            List<String> errors = new ArrayList<>();
            int successCount = 0;
            int errorCount = 0;

            try {
                // 分批处理，避免大事务
                int batchSize = properties.performance().dbBatchSize();
                for (int i = 0; i < priceHistoryList.size(); i += batchSize) {
                    List<PriceHistory> batch = priceHistoryList.subList(i, Math.min(i + batchSize, priceHistoryList.size()));

                    try {
                        // 直接批量插入，无需ID映射查询（避免N+1问题）
                        int insertedCount = batchInsertPriceHistories(batch);
                        successCount += insertedCount;
                        logger.debug("价格历史批次保存完成：{}/{}", (i / batchSize) + 1,
                            (priceHistoryList.size() + batchSize - 1) / batchSize);

                    } catch (Exception e) {
                        String error = String.format("价格历史批次保存异常：%s", e.getMessage());
                        logger.error(error, e);
                        errors.add(error);
                        errorCount += batch.size();
                    }
                }

            } catch (Exception e) {
                String error = String.format("价格历史数据保存异常：%s", e.getMessage());
                logger.error(error, e);
                errors.add(error);
                errorCount = priceHistoryList.size();
            }

            StorageResult result = new StorageResult(successCount, errorCount, errors);
            logger.info("从商品数据提取的价格历史保存完成：成功{}条，失败{}条", successCount, errorCount);

            return result;
        });
    }

    /**
     * 在更新商品数据之前，将当前数据库中的数据保存到价格历史表
     * 遵循数据流程：从数据库读取现有数据 → 保存到price_history表
     *
     * @param newGoodsList 即将保存的新商品数据列表
     */
    private void saveCurrentDataToPriceHistory(List<SkinGoods> newGoodsList) {
        try {
            // 提取goods_id和platform信息
            List<Integer> goodsIds = newGoodsList.stream()
                .map(SkinGoods::getGoodsId)
                .toList();

            String platform = newGoodsList.isEmpty() ? "" : newGoodsList.get(0).getPlatform();

            // 查询数据库中的当前数据
            List<SkinGoods> currentGoods = skinGoodsRepository.findByGoodsIdInAndPlatform(goodsIds, platform);

            if (!currentGoods.isEmpty()) {
                logger.debug("找到{}条现有商品数据，保存到价格历史表", currentGoods.size());

                // 将现有数据转换为价格历史记录并保存
                List<PriceHistory> priceHistories = currentGoods.stream()
                    .map(goods -> convertGoodsToHistoryRecord(goods, platform))
                    .toList();

                // 批量插入价格历史记录
                if (!priceHistories.isEmpty()) {
                    batchInsertPriceHistories(priceHistories);
                    logger.debug("成功保存{}条价格历史记录", priceHistories.size());
                }
            } else {
                logger.debug("未找到现有商品数据，跳过价格历史保存");
            }

        } catch (Exception e) {
            logger.warn("保存价格历史记录时出现异常：{}", e.getMessage(), e);
            // 不抛出异常，确保商品数据保存不受影响
        }
    }

    /**
     * 将商品数据转换为价格历史记录
     * 直接映射数据库字段，不进行业务逻辑计算
     *
     * @param goods 商品数据
     * @param platform 平台名称
     * @return 价格历史记录
     */
    private PriceHistory convertGoodsToHistoryRecord(SkinGoods goods, String platform) {
        PriceHistory history = new PriceHistory();

        // 基本信息直接映射
        history.setSkinGoodsId(goods.getId());
        history.setGoodsName(goods.getMarketHashName());
        history.setDataSource(platform);

        // 价格信息直接映射（不进行计算）
        history.setPrice(goods.getSellMinPrice());
        history.setPriceUsd(null); // API未提供USD价格
        history.setPriceChange(goods.getPriceAlterValue7d());

        // 处理价格变化百分比的数据范围限制 (DECIMAL(8,4): -9999.9999 到 9999.9999)
        BigDecimal priceChangePercent = goods.getPriceAlterPercentage7d();
        if (priceChangePercent != null) {
            if (priceChangePercent.compareTo(new BigDecimal("9999.9999")) > 0) {
                priceChangePercent = new BigDecimal("9999.9999");
            } else if (priceChangePercent.compareTo(new BigDecimal("-9999.9999")) < 0) {
                priceChangePercent = new BigDecimal("-9999.9999");
            }
        }
        history.setPriceChangePercent(priceChangePercent);

        history.setVolume(goods.getSellMaxNum());

        // 记录信息
        history.setRecordType((byte) 1); // 1: 定时采集
        history.setRecordTime(LocalDateTime.now());

        // 其他字段使用默认值
        history.setExchangeRate(null);
        history.setTrend((byte) 0);
        history.setVolatility(null);
        history.setIsAbnormal((byte) 0);
        history.setAbnormalReason(null);
        history.setDataQualityScore((byte) 90);
        history.setExtraInfo(null);
        history.setCreatedBy("system");
        history.setUpdatedBy("system");
        history.setRemark("数据更新前的历史记录");
        history.setDeleted((byte) 0);

        return history;
    }

    /**
     * 根据goods_id列表和平台查询已保存的商品数据
     *
     * @param goodsIds 商品ID列表
     * @param platform 平台名称
     * @return 已保存的商品列表（包含数据库主键ID）
     */
    public List<SkinGoods> findGoodsByGoodsIdsAndPlatform(List<Integer> goodsIds, String platform) {
        try {
            logger.debug("查询{}平台{}个商品的已保存数据", platform, goodsIds.size());

            List<SkinGoods> result = skinGoodsRepository.findByGoodsIdInAndPlatform(goodsIds, platform);

            logger.debug("{}平台找到{}条已保存的商品数据", platform, result.size());
            return result;

        } catch (Exception e) {
            logger.error("查询{}平台商品数据异常", platform, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据平台获取所有商品ID
     * 
     * @param platform 平台名称
     * @return 商品ID列表
     */
    public List<Integer> getGoodsIdsByPlatform(String platform) {
        logger.debug("获取{}平台的所有商品ID", platform);
        return skinGoodsRepository.findGoodsIdsByPlatform(platform);
    }

    /**
     * 统计平台商品数量
     * 
     * @param platform 平台名称
     * @return 商品数量
     */
    public long countGoodsByPlatform(String platform) {
        return skinGoodsRepository.countByPlatform(platform);
    }

    /**
     * 清理指定平台的数据
     * 
     * @param platform 平台名称
     * @return 清理结果
     */
    @Transactional
    public StorageResult cleanPlatformData(String platform) {
        logger.info("开始清理{}平台的数据", platform);
        
        List<String> errors = new ArrayList<>();
        int successCount = 0;
        int errorCount = 0;
        
        try {
            // 删除饰品数据（级联删除价格历史）
            int deletedCount = skinGoodsRepository.deleteByPlatform(platform);
            successCount = deletedCount;
            
            logger.info("{}平台数据清理完成，删除{}条记录", platform, deletedCount);
            
        } catch (Exception e) {
            String error = String.format("清理%s平台数据异常：%s", platform, e.getMessage());
            logger.error(error, e);
            errors.add(error);
            errorCount = 1;
        }
        
        return new StorageResult(successCount, errorCount, errors);
    }

    /**
     * 获取数据库统计信息
     * 
     * @return 统计信息
     */
    public DatabaseStats getDatabaseStats() {
        try {
            long totalGoods = skinGoodsRepository.count();
            long totalPriceHistory = priceHistoryRepository.count();
            
            // 按平台统计商品数量
            List<String> platforms = List.of("BUFF", "IGXE", "STEAM", "YP");
            var platformStats = platforms.stream()
                .collect(java.util.stream.Collectors.toMap(
                    platform -> platform,
                    platform -> skinGoodsRepository.countByPlatform(platform)
                ));
            
            return new DatabaseStats(totalGoods, totalPriceHistory, platformStats);
            
        } catch (Exception e) {
            logger.error("获取数据库统计信息异常", e);
            return new DatabaseStats(0, 0, java.util.Map.of());
        }
    }

    /**
     * 存储结果
     */
    public record StorageResult(
        int successCount,
        int errorCount,
        List<String> errors
    ) {
        public boolean isSuccess() {
            return errorCount == 0;
        }
        
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
        
        public int getTotalCount() {
            return successCount + errorCount;
        }
        
        public double getSuccessRate() {
            int total = getTotalCount();
            return total > 0 ? (double) successCount / total : 0.0;
        }
    }

    /**
     * 数据库统计信息
     */
    public record DatabaseStats(
        long totalGoods,
        long totalPriceHistory,
        java.util.Map<String, Long> platformStats
    ) {}

    /**
     * 使用JdbcTemplate进行真正的批量插入
     * 解决JPA IDENTITY主键策略无法批量插入的问题
     *
     * @param priceHistories 价格历史记录列表
     * @return 插入的记录数
     */
    @Transactional
    private int batchInsertPriceHistories(List<PriceHistory> priceHistories) {
        String sql = """
            INSERT INTO price_history (
                skin_goods_id, goods_name, price, price_usd, price_change,
                price_change_percent, volume, data_source, record_type, record_time,
                exchange_rate, trend, volatility, is_abnormal, abnormal_reason,
                data_quality_score, extra_info, created_by, updated_by, remark,
                deleted, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            """;

        // 准备批量参数
        List<Object[]> batchArgs = priceHistories.stream()
            .map(ph -> new Object[]{
                ph.getSkinGoodsId(),
                ph.getGoodsName(),
                ph.getPrice(),
                ph.getPriceUsd(),
                ph.getPriceChange(),
                ph.getPriceChangePercent(),
                ph.getVolume(),
                ph.getDataSource(),
                ph.getRecordType(),
                ph.getRecordTime(),
                ph.getExchangeRate(),
                ph.getTrend(),
                ph.getVolatility(),
                ph.getIsAbnormal(),
                ph.getAbnormalReason(),
                ph.getDataQualityScore(),
                ph.getExtraInfo(),
                ph.getCreatedBy(),
                ph.getUpdatedBy(),
                ph.getRemark(),
                ph.getDeleted()
            })
            .toList();

        // 执行批量插入
        int[] results = jdbcTemplate.batchUpdate(sql, batchArgs);
        return results.length;
    }
}
