package com.skin86.api.service;

import com.skin86.api.config.Skin86Properties;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 监控统计服务
 * 
 * 负责收集和管理系统运行时的各种统计指标
 * 集成Micrometer进行指标收集和暴露
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class MonitoringService {

    private static final Logger logger = LoggerFactory.getLogger(MonitoringService.class);

    private final MeterRegistry meterRegistry;
    private final Skin86Properties properties;
    
    // 计数器
    private final Counter apiRequestCounter;
    private final Counter apiSuccessCounter;
    private final Counter apiErrorCounter;
    private final Counter dataProcessedCounter;
    private final Counter dataSavedCounter;
    
    // 计时器
    private final Timer apiRequestTimer;
    private final Timer dataProcessTimer;
    private final Timer dataSaveTimer;
    
    // 运行时统计
    private final Map<String, TaskExecutionStats> taskStats = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> platformCounters = new ConcurrentHashMap<>();
    private final AtomicLong totalMemoryUsage = new AtomicLong();
    private final AtomicLong activeThreads = new AtomicLong();

    public MonitoringService(MeterRegistry meterRegistry, Skin86Properties properties) {
        this.meterRegistry = meterRegistry;
        this.properties = properties;
        
        // 初始化计数器
        this.apiRequestCounter = Counter.builder("skin86.api.requests.total")
            .description("Total API requests")
            .register(meterRegistry);
            
        this.apiSuccessCounter = Counter.builder("skin86.api.requests.success")
            .description("Successful API requests")
            .register(meterRegistry);
            
        this.apiErrorCounter = Counter.builder("skin86.api.requests.error")
            .description("Failed API requests")
            .register(meterRegistry);
            
        this.dataProcessedCounter = Counter.builder("skin86.data.processed.total")
            .description("Total processed data records")
            .register(meterRegistry);
            
        this.dataSavedCounter = Counter.builder("skin86.data.saved.total")
            .description("Total saved data records")
            .register(meterRegistry);
        
        // 初始化计时器
        this.apiRequestTimer = Timer.builder("skin86.api.request.duration")
            .description("API request duration")
            .register(meterRegistry);
            
        this.dataProcessTimer = Timer.builder("skin86.data.process.duration")
            .description("Data processing duration")
            .register(meterRegistry);
            
        this.dataSaveTimer = Timer.builder("skin86.data.save.duration")
            .description("Data saving duration")
            .register(meterRegistry);
        
        // 注册Gauge指标
        Gauge.builder("skin86.memory.usage", this, MonitoringService::getCurrentMemoryUsage)
            .description("Current memory usage in bytes")
            .register(meterRegistry);

        Gauge.builder("skin86.threads.active", this, MonitoringService::getCurrentActiveThreads)
            .description("Current active threads")
            .register(meterRegistry);
        
        // 启动监控线程
        if (properties.monitoring().enableStats()) {
            startMonitoringThread();
        }
    }

    /**
     * 记录API请求
     *
     * @param platform 平台
     * @param endpoint 端点
     */
    public void recordApiRequest(String platform, String endpoint) {
        Counter.builder("skin86.api.requests.total")
            .tag("platform", platform)
            .tag("endpoint", endpoint)
            .register(meterRegistry)
            .increment();
    }

    /**
     * 记录API成功
     *
     * @param platform 平台
     * @param endpoint 端点
     * @param duration 耗时（毫秒）
     */
    public void recordApiSuccess(String platform, String endpoint, long duration) {
        Counter.builder("skin86.api.requests.success")
            .tag("platform", platform)
            .tag("endpoint", endpoint)
            .register(meterRegistry)
            .increment();
        apiRequestTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
    }

    /**
     * 记录API错误
     *
     * @param platform 平台
     * @param endpoint 端点
     * @param errorType 错误类型
     */
    public void recordApiError(String platform, String endpoint, String errorType) {
        Counter.builder("skin86.api.requests.error")
            .tag("platform", platform)
            .tag("endpoint", endpoint)
            .tag("error", errorType)
            .register(meterRegistry)
            .increment();
    }

    /**
     * 记录数据处理
     *
     * @param platform 平台
     * @param dataType 数据类型
     * @param count 处理数量
     * @param duration 耗时（毫秒）
     */
    public void recordDataProcessed(String platform, String dataType, long count, long duration) {
        Counter.builder("skin86.data.processed.total")
            .tag("platform", platform)
            .tag("type", dataType)
            .register(meterRegistry)
            .increment(count);
        dataProcessTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);

        // 更新平台计数器
        platformCounters.computeIfAbsent(platform + "_processed", k -> new AtomicLong(0))
            .addAndGet(count);
    }

    /**
     * 记录数据保存
     *
     * @param platform 平台
     * @param dataType 数据类型
     * @param count 保存数量
     * @param duration 耗时（毫秒）
     */
    public void recordDataSaved(String platform, String dataType, long count, long duration) {
        Counter.builder("skin86.data.saved.total")
            .tag("platform", platform)
            .tag("type", dataType)
            .register(meterRegistry)
            .increment(count);
        dataSaveTimer.record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);

        // 更新平台计数器
        platformCounters.computeIfAbsent(platform + "_saved", k -> new AtomicLong(0))
            .addAndGet(count);
    }

    /**
     * 记录任务开始
     * 
     * @param taskName 任务名称
     */
    public void recordTaskStart(String taskName) {
        TaskExecutionStats stats = taskStats.computeIfAbsent(taskName, 
            k -> new TaskExecutionStats(taskName));
        stats.recordStart();
        
        logger.info("任务开始：{}", taskName);
    }

    /**
     * 记录任务完成
     * 
     * @param taskName 任务名称
     */
    public void recordTaskComplete(String taskName) {
        TaskExecutionStats stats = taskStats.get(taskName);
        if (stats != null) {
            stats.recordComplete();
            logger.info("任务完成：{}，耗时：{}ms", taskName, stats.getLastDuration());
        }
    }

    /**
     * 记录任务错误
     * 
     * @param taskName 任务名称
     * @param error 错误信息
     */
    public void recordTaskError(String taskName, String error) {
        TaskExecutionStats stats = taskStats.get(taskName);
        if (stats != null) {
            stats.recordError(error);
            logger.error("任务错误：{}，错误：{}", taskName, error);
        }
    }

    /**
     * 获取执行统计信息
     * 
     * @return 统计信息
     */
    public ExecutionStats getExecutionStats() {
        Map<String, Object> taskStatsMap = taskStats.entrySet().stream()
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    Map<String, Object> stats = new java.util.HashMap<>();
                    stats.put("totalExecutions", entry.getValue().getTotalExecutions());
                    stats.put("successCount", entry.getValue().getSuccessCount());
                    stats.put("errorCount", entry.getValue().getErrorCount());
                    stats.put("lastDuration", entry.getValue().getLastDuration());
                    stats.put("averageDuration", entry.getValue().getAverageDuration());
                    stats.put("lastExecutionTime", entry.getValue().getLastExecutionTime());
                    return stats;
                }
            ));

        Map<String, Long> platformStatsMap = platformCounters.entrySet().stream()
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().get()
            ));

        return new ExecutionStats(
            getCurrentMemoryUsage(),
            getCurrentActiveThreads(),
            taskStatsMap,
            platformStatsMap,
            LocalDateTime.now()
        );
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        taskStats.clear();
        platformCounters.clear();
        logger.info("统计信息已重置");
    }

    /**
     * 获取当前内存使用量
     * 
     * @return 内存使用量（字节）
     */
    private double getCurrentMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        return totalMemory - freeMemory;
    }

    /**
     * 获取当前活跃线程数
     * 
     * @return 活跃线程数
     */
    private double getCurrentActiveThreads() {
        return Thread.activeCount();
    }

    /**
     * 启动监控线程
     */
    private void startMonitoringThread() {
        Thread.ofVirtual().start(() -> {
            while (true) {
                try {
                    Thread.sleep(properties.monitoring().statsInterval().toMillis());
                    
                    // 更新内存使用量
                    totalMemoryUsage.set((long) getCurrentMemoryUsage());
                    activeThreads.set((long) getCurrentActiveThreads());
                    
                    // 检查内存阈值
                    if (properties.monitoring().enableMemoryMonitor()) {
                        checkMemoryThreshold();
                    }
                    
                    // 定期输出统计信息
                    if (logger.isDebugEnabled()) {
                        logCurrentStats();
                    }
                    
                } catch (InterruptedException e) {
                    logger.info("监控线程被中断");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("监控线程异常", e);
                }
            }
        });
    }

    /**
     * 检查内存阈值
     */
    private void checkMemoryThreshold() {
        Runtime runtime = Runtime.getRuntime();
        double memoryUsageRatio = (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory();
        
        if (memoryUsageRatio > properties.monitoring().memoryThreshold()) {
            logger.warn("内存使用率过高：{:.2f}%，建议进行垃圾回收", memoryUsageRatio * 100);
            
            // 触发垃圾回收
            System.gc();
        }
    }

    /**
     * 输出当前统计信息
     */
    private void logCurrentStats() {
        ExecutionStats stats = getExecutionStats();
        logger.debug("当前统计信息：内存使用{}MB，活跃线程{}，任务统计{}", 
            stats.memoryUsage() / 1024 / 1024,
            stats.activeThreads(),
            stats.taskStats().size());
    }

    /**
     * 任务执行统计
     */
    private static class TaskExecutionStats {
        private final String taskName;
        private final AtomicLong totalExecutions = new AtomicLong(0);
        private final AtomicLong successCount = new AtomicLong(0);
        private final AtomicLong errorCount = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private volatile long lastDuration = 0;
        private volatile LocalDateTime lastExecutionTime;
        private volatile LocalDateTime startTime;

        public TaskExecutionStats(String taskName) {
            this.taskName = taskName;
        }

        public void recordStart() {
            startTime = LocalDateTime.now();
            totalExecutions.incrementAndGet();
        }

        public void recordComplete() {
            if (startTime != null) {
                lastDuration = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
                totalDuration.addAndGet(lastDuration);
                lastExecutionTime = LocalDateTime.now();
                successCount.incrementAndGet();
            }
        }

        public void recordError(String error) {
            if (startTime != null) {
                lastDuration = java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
                lastExecutionTime = LocalDateTime.now();
                errorCount.incrementAndGet();
            }
        }

        public long getTotalExecutions() { return totalExecutions.get(); }
        public long getSuccessCount() { return successCount.get(); }
        public long getErrorCount() { return errorCount.get(); }
        public long getLastDuration() { return lastDuration; }
        public LocalDateTime getLastExecutionTime() { return lastExecutionTime; }
        
        public double getAverageDuration() {
            long executions = successCount.get() + errorCount.get();
            return executions > 0 ? (double) totalDuration.get() / executions : 0.0;
        }
    }

    /**
     * 执行统计信息
     */
    public record ExecutionStats(
        double memoryUsage,
        double activeThreads,
        Map<String, Object> taskStats,
        Map<String, Long> platformStats,
        LocalDateTime timestamp
    ) {}
}
