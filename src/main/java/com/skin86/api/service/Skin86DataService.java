package com.skin86.api.service;

import com.skin86.api.config.Skin86Properties;
import com.skin86.api.scheduler.CheckpointManager;
import com.skin86.api.scheduler.TaskCheckpoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Skin86数据服务
 * 整合数据获取、存储、监控等功能的主要业务服务
 * 提供高级的数据处理和管理功能
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Service
public class Skin86DataService {

    private static final Logger logger = LoggerFactory.getLogger(Skin86DataService.class);
    private final DateTimeFormatter dateFormatter;

    private final DataFetchService dataFetchService;
    private final DataStorageService dataStorageService;
    private final MonitoringService monitoringService;
    private final CheckpointManager checkpointManager;
    private final Skin86Properties properties;

    public Skin86DataService(DataFetchService dataFetchService,
                            DataStorageService dataStorageService,
                            MonitoringService monitoringService,
                            CheckpointManager checkpointManager,
                            Skin86Properties properties) {
        this.dataFetchService = dataFetchService;
        this.dataStorageService = dataStorageService;
        this.monitoringService = monitoringService;
        this.checkpointManager = checkpointManager;
        this.properties = properties;
        this.dateFormatter = DateTimeFormatter.ofPattern(properties.format().datePattern());
    }

    /**
     * 执行完整的数据同步流程
     * 
     * @param platform 平台名称
     * @param syncType 同步类型（goods/price/both）
     * @return 同步结果
     */
    public CompletableFuture<SyncResult> executeFullSync(String platform, String syncType) {
        return CompletableFuture.supplyAsync(() -> {
            logger.info("开始执行{}平台完整数据同步，类型：{}", platform, syncType);
            
            SyncResult.Builder resultBuilder = new SyncResult.Builder(platform, syncType);
            
            try {
                // 记录同步开始
                monitoringService.recordTaskStart("full_sync_" + platform);
                
                // 执行商品数据同步
                if ("goods".equals(syncType) || "both".equals(syncType)) {
                    var goodsResult = syncGoodsData(platform);
                    resultBuilder.withGoodsResult(goodsResult);
                }
                
                // 执行价格数据同步
                if ("price".equals(syncType) || "both".equals(syncType)) {
                    var priceResult = syncPriceData(platform);
                    resultBuilder.withPriceResult(priceResult);
                }
                
                // 记录同步完成
                monitoringService.recordTaskComplete("full_sync_" + platform);
                
                SyncResult result = resultBuilder.build();
                logger.info("{}平台数据同步完成：{}", platform, result);
                
                return result;
                
            } catch (Exception e) {
                logger.error("{}平台数据同步异常", platform, e);
                monitoringService.recordTaskError("full_sync_" + platform, e.getMessage());
                return resultBuilder.withError(e.getMessage()).build();
            }
        });
    }

    /**
     * 同步商品数据
     * 每次都获取最新的完整商品数据，确保数据完整性
     *
     * @param platform 平台名称
     * @return 同步结果
     */
    private DataSyncResult syncGoodsData(String platform) {
        logger.info("开始同步{}平台商品数据（获取最新完整数据）", platform);

        try {
            // 每次都从第1页开始获取最新数据，确保完整性
            // 不依赖断点，因为我们需要确保商品数据的完整性和准确性
            int startPage = 1;

            // 获取最新的完整商品数据
            long startTime = System.currentTimeMillis();
            var fetchResult = dataFetchService.fetchGoodsData(platform, startPage, null).join();
            long fetchDuration = System.currentTimeMillis() - startTime;
            
            // 记录获取统计
            monitoringService.recordDataProcessed(platform, "goods", 
                fetchResult.getTotalRecords(), fetchDuration);
            
            if (fetchResult.data().isEmpty()) {
                return new DataSyncResult(0, 0, 0, List.of("没有获取到商品数据"));
            }
            
            // 保存数据
            startTime = System.currentTimeMillis();
            var storageResult = dataStorageService.saveSkinGoods(fetchResult.data()).join();
            long saveDuration = System.currentTimeMillis() - startTime;
            
            // 记录保存统计
            monitoringService.recordDataSaved(platform, "goods", 
                storageResult.successCount(), saveDuration);
            
            // 清理断点（因为我们现在每次都获取完整数据，不需要断点续传）
            if (properties.scheduler().enableCheckpoint() && storageResult.successCount() > 0) {
                // 清理旧的断点，因为我们已经获取了完整的最新数据
                checkpointManager.removeCheckpoint("goods_" + platform);
                logger.info("已清理{}平台商品数据断点，完成完整数据同步", platform);
            }
            
            return new DataSyncResult(
                fetchResult.getTotalRecords(),
                storageResult.successCount(),
                storageResult.errorCount(),
                fetchResult.errors()
            );
            
        } catch (Exception e) {
            logger.error("同步{}平台商品数据异常", platform, e);
            return new DataSyncResult(0, 0, 1, List.of("同步异常：" + e.getMessage()));
        }
    }

    /**
     * 同步价格数据
     * 先确保商品数据最新，再获取价格数据
     *
     * @param platform 平台名称
     * @return 同步结果
     */
    private DataSyncResult syncPriceData(String platform) {
        logger.info("开始同步{}平台价格数据", platform);

        try {
            // 1. 先确保商品数据是最新的
            logger.info("步骤1：确保{}平台商品数据最新", platform);
            DataSyncResult goodsResult = syncGoodsData(platform);
            if (!goodsResult.isSuccess()) {
                logger.warn("商品数据同步失败，继续使用现有数据进行价格同步");
            }

            // 2. 获取最新的商品ID列表
            List<Integer> goodsIds = dataStorageService.getGoodsIdsByPlatform(platform);
            if (goodsIds.isEmpty()) {
                return new DataSyncResult(0, 0, 0, List.of("没有商品数据，无法进行价格同步"));
            }

            logger.info("步骤2：基于{}个商品获取价格数据", goodsIds.size());
            
            String today = LocalDate.now().format(dateFormatter);
            
            // 获取价格数据
            long startTime = System.currentTimeMillis();
            var fetchResult = dataFetchService.fetchPriceData(platform, goodsIds, today).join();
            long fetchDuration = System.currentTimeMillis() - startTime;
            
            // 记录获取统计
            monitoringService.recordDataProcessed(platform, "price", 
                fetchResult.getTotalRecords(), fetchDuration);
            
            if (fetchResult.data().isEmpty()) {
                return new DataSyncResult(0, 0, 0, List.of("没有获取到价格数据"));
            }
            
            // 保存数据
            startTime = System.currentTimeMillis();
            var storageResult = dataStorageService.savePriceHistory(fetchResult.data()).join();
            long saveDuration = System.currentTimeMillis() - startTime;
            
            // 记录保存统计
            monitoringService.recordDataSaved(platform, "price", 
                storageResult.successCount(), saveDuration);
            
            return new DataSyncResult(
                fetchResult.getTotalRecords(),
                storageResult.successCount(),
                storageResult.errorCount(),
                fetchResult.errors()
            );
            
        } catch (Exception e) {
            logger.error("同步{}平台价格数据异常", platform, e);
            return new DataSyncResult(0, 0, 1, List.of("同步异常：" + e.getMessage()));
        }
    }

    /**
     * 获取系统概览信息
     * 
     * @return 概览信息
     */
    public SystemOverview getSystemOverview() {
        var dbStats = dataStorageService.getDatabaseStats();
        var execStats = monitoringService.getExecutionStats();
        var checkpointStats = checkpointManager.getCheckpointStats();
        
        return new SystemOverview(
            dbStats.totalGoods(),
            dbStats.totalPriceHistory(),
            dbStats.platformStats(),
            execStats.memoryUsage(),
            execStats.activeThreads(),
            checkpointStats.totalCheckpoints(),
            checkpointStats.activeCheckpoints()
        );
    }

    /**
     * 数据同步结果
     */
    public record DataSyncResult(
        int fetchedCount,
        int savedCount,
        int errorCount,
        List<String> errors
    ) {
        public boolean isSuccess() {
            return errorCount == 0;
        }
    }

    /**
     * 完整同步结果
     */
    public static class SyncResult {
        private final String platform;
        private final String syncType;
        private DataSyncResult goodsResult;
        private DataSyncResult priceResult;
        private String error;

        private SyncResult(String platform, String syncType) {
            this.platform = platform;
            this.syncType = syncType;
        }

        public static class Builder {
            private final SyncResult result;

            public Builder(String platform, String syncType) {
                this.result = new SyncResult(platform, syncType);
            }

            public void withGoodsResult(DataSyncResult goodsResult) {
                result.goodsResult = goodsResult;
            }

            public void withPriceResult(DataSyncResult priceResult) {
                result.priceResult = priceResult;
            }

            public Builder withError(String error) {
                result.error = error;
                return this;
            }

            public SyncResult build() {
                return result;
            }
        }

        // Getters
        public String getPlatform() { return platform; }
        public String getSyncType() { return syncType; }
        public DataSyncResult getGoodsResult() { return goodsResult; }
        public DataSyncResult getPriceResult() { return priceResult; }
        public String getError() { return error; }

        public boolean isSuccess() {
            return error == null && 
                   (goodsResult == null || goodsResult.isSuccess()) && 
                   (priceResult == null || priceResult.isSuccess());
        }

        @Override
        public String toString() {
            return String.format("SyncResult{platform='%s', syncType='%s', success=%s}", 
                platform, syncType, isSuccess());
        }
    }

    /**
     * 系统概览信息
     */
    public record SystemOverview(
        long totalGoods,
        long totalPriceHistory,
        java.util.Map<String, Long> platformStats,
        double memoryUsage,
        double activeThreads,
        int totalCheckpoints,
        int activeCheckpoints
    ) {}
}
