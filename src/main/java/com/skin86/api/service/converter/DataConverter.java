package com.skin86.api.service.converter;

import com.skin86.api.dto.response.BatchPricesResponse;
import com.skin86.api.dto.response.GoodsListResponse;
import com.skin86.api.entity.PriceHistory;
import com.skin86.api.entity.SkinGoods;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.skin86.api.config.Skin86Properties;

/**
 * 数据转换器 - 纯数据透传
 * 负责将API响应数据直接转换为数据库实体对象
 * 不进行任何业务验证、质量检查或数据处理
 * 实现API数据到数据库的纯透传
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Component
public class DataConverter {

    private final DateTimeFormatter dateFormatter;
    private final Skin86Properties properties;

    /**
     * 将API商品数据转换为SkinGoods实体
     */
    public DataConverter(Skin86Properties properties) {
        this.properties = properties;
        this.dateFormatter = DateTimeFormatter.ofPattern(properties.format().datePattern());
    }

    @Override
    public String toString() {
        return "DataConverter using pattern " + properties.format().datePattern();
    }

    public SkinGoods convertToSkinGoods(GoodsListResponse.GoodsItem item, String platform) {
        SkinGoods goods = new SkinGoods();
        
        // 基本信息
        goods.setGoodsId(item.goodsId().intValue());
        goods.setPlatformId(item.platformId());
        goods.setMarketName(item.marketName());
        goods.setMarketHashName(item.marketHashName());
        goods.setPlatform(platform);
        
        // 价格信息
        goods.setSellMinPrice(item.sellMinPrice());
        goods.setSellMaxNum(item.sellMaxNum());
        goods.setSellValuation(item.sellValuation());
        goods.setBuyMaxPrice(item.buyMaxPrice());
        goods.setBuyMaxNum(item.buyMaxNum());
        
        // 价格变化信息
        goods.setPriceAlterPercentage7d(item.priceAlterPercentage7d());
        goods.setPriceAlterValue7d(item.priceAlterValue7d());
        
        // 分类和属性信息
        goods.setCategoryGroupName(item.categoryGroupName());
        goods.setRarityColor(item.rarityColor());
        goods.setRarity(item.rarity());
        goods.setExterior(item.exterior());
        
        // 其他信息
        goods.setIconUrl(item.iconUrl());
        goods.setIsFollow(item.isFollow());
        goods.setRedirectUrl(item.redirectUrl());
        
        return goods;
    }

    /**
     * 将API价格数据转换为PriceHistory实体
     * 
     * @param item API价格项
     * @param platform 平台名称
     * @return PriceHistory实体
     */
    public PriceHistory convertToPriceHistory(BatchPricesResponse.PriceItem item, String platform) {
        PriceHistory priceHistory = new PriceHistory();
        
        // 基本信息
        priceHistory.setSkinGoodsId(item.goodsId().intValue());
        priceHistory.setGoodsName(item.marketHashName());
        priceHistory.setDataSource(platform);
        
        // 价格信息
        priceHistory.setPrice(item.price() != null ? item.price() : BigDecimal.ZERO);
        priceHistory.setPriceChange(null); // 新API格式中没有价格变化数据
        priceHistory.setPriceChangePercent(null); // 新API格式中没有价格变化百分比
        priceHistory.setVolume(null); // 新API格式中没有交易量数据
        
        // 记录信息
        priceHistory.setRecordType((byte) 1); // 1: 定时采集
        priceHistory.setRecordTime(LocalDateTime.now());
        
        // 计算趋势（基于最大最小价格差异）
        if (item.maxPrice() != null && item.minPrice() != null) {
            BigDecimal priceDiff = item.maxPrice().subtract(item.minPrice());
            if (priceDiff.compareTo(BigDecimal.ZERO) > 0) {
                priceHistory.setTrend((byte) 1); // 有价格波动，设为上涨
            } else {
                priceHistory.setTrend((byte) 0); // 无价格波动，设为持平
            }
        } else {
            priceHistory.setTrend((byte) 0); // 默认持平
        }
        
        // 跳过所有数据质量评分和异常检测 - 纯数据透传
        priceHistory.setDataQualityScore((byte) 100); // 固定值，不进行计算
        priceHistory.setIsAbnormal((byte) 0); // 固定值，不进行检测
        priceHistory.setAbnormalReason(null);
        
        return priceHistory;
    }

    // 已移除所有数据验证和计算方法 - 实现纯数据透传

    /**
     * 转换日期字符串为LocalDateTime
     * 
     * @param dateStr 日期字符串 (yyyy-MM-dd)
     * @return LocalDateTime
     */
    public LocalDateTime convertToDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return LocalDateTime.now();
        }
        
        try {
            return LocalDateTime.parse(dateStr + "T00:00:00");
        } catch (Exception e) {
            return LocalDateTime.now();
        }
    }

    /**
     * 安全转换BigDecimal
     * 
     * @param value 原始值
     * @param defaultValue 默认值
     * @return BigDecimal值
     */
    public BigDecimal safeToBigDecimal(Object value, BigDecimal defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 安全转换Integer
     * 
     * @param value 原始值
     * @param defaultValue 默认值
     * @return Integer值
     */
    public Integer safeToInteger(Object value, Integer defaultValue) {
        if (value == null) {
            return defaultValue;
        }
        
        if (value instanceof Integer) {
            return (Integer) value;
        }
        
        try {
            return Integer.valueOf(value.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public DateTimeFormatter getDateFormatter() {
        return dateFormatter;
    }

    /**
     * 从商品数据提取价格历史记录
     * 将商品当前价格信息转换为价格历史记录
     *
     * @param goods 商品实体
     * @param platform 平台名称
     * @return 价格历史记录
     */
    public PriceHistory extractPriceHistoryFromGoods(SkinGoods goods, String platform) {
        PriceHistory priceHistory = new PriceHistory();

        // 基本信息
        // 使用数据库主键ID
        priceHistory.setSkinGoodsId(goods.getId());
        priceHistory.setGoodsName(goods.getMarketHashName());
        priceHistory.setDataSource(platform);

        // 价格信息 - 优先使用有效的sell_min_price，否则使用buy_max_price
        BigDecimal mainPrice = BigDecimal.ZERO;
        if (goods.getSellMinPrice() != null && goods.getSellMinPrice().compareTo(BigDecimal.ZERO) > 0) {
            mainPrice = goods.getSellMinPrice();
        } else if (goods.getBuyMaxPrice() != null && goods.getBuyMaxPrice().compareTo(BigDecimal.ZERO) > 0) {
            mainPrice = goods.getBuyMaxPrice();
        }
        priceHistory.setPrice(mainPrice);

        // 价格变化信息（从商品数据中获取）
        priceHistory.setPriceChange(goods.getPriceAlterValue7d());
        priceHistory.setPriceChangePercent(goods.getPriceAlterPercentage7d());

        // 交易量信息（使用sell_max_num作为参考）
        priceHistory.setVolume(goods.getSellMaxNum());

        // 记录信息
        priceHistory.setRecordType((byte) 1); // 1: 定时采集
        priceHistory.setRecordTime(LocalDateTime.now());

        // 趋势分析（基于7天价格变化）
        if (goods.getPriceAlterValue7d() != null) {
            if (goods.getPriceAlterValue7d().compareTo(BigDecimal.ZERO) > 0) {
                priceHistory.setTrend((byte) 1); // 上涨
            } else if (goods.getPriceAlterValue7d().compareTo(BigDecimal.ZERO) < 0) {
                priceHistory.setTrend((byte) -1); // 下跌
            } else {
                priceHistory.setTrend((byte) 0); // 持平
            }
        } else {
            priceHistory.setTrend((byte) 0); // 默认持平
        }

        // 波动性计算（基于买卖价差）
        if (goods.getSellMinPrice() != null && goods.getBuyMaxPrice() != null
            && mainPrice.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal spread = goods.getSellMinPrice().subtract(goods.getBuyMaxPrice());
            BigDecimal volatility = spread.divide(mainPrice, 4, java.math.RoundingMode.HALF_UP);
            priceHistory.setVolatility(volatility);
        }

        // 数据质量和异常检测
        priceHistory.setDataQualityScore((byte) 95); // 来自商品数据，质量较高
        priceHistory.setIsAbnormal((byte) 0); // 默认正常
        priceHistory.setAbnormalReason(null);

        // 扩展信息（JSON格式存储额外的价格信息）
        String extraInfo = String.format(
            "{\"sell_min_price\":%s,\"sell_max_num\":%d,\"sell_valuation\":%s,\"buy_max_price\":%s,\"buy_max_num\":%d}",
            goods.getSellMinPrice() != null ? goods.getSellMinPrice().toString() : "null",
            goods.getSellMaxNum() != null ? goods.getSellMaxNum() : 0,
            goods.getSellValuation() != null ? goods.getSellValuation().toString() : "null",
            goods.getBuyMaxPrice() != null ? goods.getBuyMaxPrice().toString() : "null",
            goods.getBuyMaxNum() != null ? goods.getBuyMaxNum() : 0
        );
        priceHistory.setExtraInfo(extraInfo);

        // 其他字段
        priceHistory.setCreatedBy("system");
        priceHistory.setUpdatedBy("system");
        priceHistory.setRemark("从商品数据自动提取");
        priceHistory.setDeleted((byte) 0);

        return priceHistory;
    }
}
