package com.skin86.api.scheduler;

import com.skin86.api.config.Skin86Properties;
import com.skin86.api.config.StartupConfig;
import com.skin86.api.service.DataFetchService;
import com.skin86.api.service.DataStorageService;
import com.skin86.api.service.MonitoringService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务调度器
 * 负责定时执行数据获取任务
 * 支持多种调度模式和断点续传
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Component("skin86TaskScheduler")
public class TaskScheduler {

    private static final Logger logger = LoggerFactory.getLogger(TaskScheduler.class);
    private final DateTimeFormatter dateFormatter;

    private final DataFetchService dataFetchService;
    private final DataStorageService dataStorageService;
    private final MonitoringService monitoringService;
    private final CheckpointManager checkpointManager;
    private final Skin86Properties properties;
    private final StartupConfig startupConfig;
    
    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    public TaskScheduler(DataFetchService dataFetchService,
                        DataStorageService dataStorageService,
                        MonitoringService monitoringService,
                        CheckpointManager checkpointManager,
                        Skin86Properties properties,
                        StartupConfig startupConfig) {
        this.dataFetchService = dataFetchService;
        this.dataStorageService = dataStorageService;
        this.monitoringService = monitoringService;
        this.checkpointManager = checkpointManager;
        this.properties = properties;
        this.startupConfig = startupConfig;
        this.dateFormatter = DateTimeFormatter.ofPattern(properties.format().datePattern());
    }

    /**
     * 应用启动完成后根据配置自动执行商品数据获取
     * 支持配置化控制是否启用、延迟时间、平台等参数
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("应用启动完成，检查启动时数据获取配置：{}", startupConfig);

        if (!startupConfig.isEnabled()) {
            logger.info("启动时数据获取已禁用，跳过自动执行");
            return;
        }

        logger.info("启动时数据获取已启用，将在{}秒后开始执行", startupConfig.getDelaySeconds());

        // 根据配置的延迟时间执行
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(startupConfig.getDelaySeconds() * 1000L);
                logger.info("开始执行启动后的商品数据获取任务，平台：{}", startupConfig.getPlatforms());

                // 根据配置执行指定平台的数据获取
                executeStartupDataFetch();

                logger.info("启动后的商品数据获取任务完成");
            } catch (Exception e) {
                logger.error("启动后的商品数据获取任务异常", e);
            }
        });
    }

    /**
     * 定时执行商品数据获取任务
     * 每天上午9点和下午6点执行
     */
    @Scheduled(cron = "0 0 9,18 * * ?")
    public void scheduleGoodsDataFetch() {
        if (!isRunning.compareAndSet(false, true)) {
            logger.warn("商品数据获取任务正在运行中，跳过本次调度");
            return;
        }

        try {
            logger.info("开始执行定时商品数据获取任务");
            monitoringService.recordTaskStart("goods_fetch");
            
            executeGoodsDataFetch().join();
            
            monitoringService.recordTaskComplete("goods_fetch");
            logger.info("定时商品数据获取任务完成");
            
        } catch (Exception e) {
            logger.error("定时商品数据获取任务异常", e);
            monitoringService.recordTaskError("goods_fetch", e.getMessage());
        } finally {
            isRunning.set(false);
        }
    }

    /**
     * 定时执行数据清理任务
     * 每天凌晨2点执行，清理过期的价格历史记录和断点
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void scheduleDataCleanup() {
        if (isRunning.get()) {
            logger.warn("其他任务正在运行中，跳过数据清理");
            return;
        }

        try {
            logger.info("开始执行定时数据清理任务");
            monitoringService.recordTaskStart("data_cleanup");

            executeDataCleanup().join();

            monitoringService.recordTaskComplete("data_cleanup");
            logger.info("定时数据清理任务完成");

        } catch (Exception e) {
            logger.error("定时数据清理任务异常", e);
            monitoringService.recordTaskError("data_cleanup", e.getMessage());
        }
    }

    /**
     * 执行商品数据获取
     *
     * @return 异步任务
     */
    public CompletableFuture<Void> executeGoodsDataFetch() {
        return CompletableFuture.runAsync(() -> {
            List<String> platforms = properties.platform().defaultPlatforms();

            for (String platform : platforms) {
                try {
                    logger.info("开始获取{}平台商品数据", platform);

                    // 检查是否需要全量更新
                    boolean needFullUpdate = shouldPerformFullUpdate(platform);
                    Integer startPage = 1;

                    if (!needFullUpdate) {
                        // 检查断点有效性
                        TaskCheckpoint checkpoint = checkpointManager.getCheckpoint("goods_" + platform);
                        if (checkpoint != null && isCheckpointValid(checkpoint)) {
                            startPage = checkpoint.lastPage() + 1;
                            logger.info("{}平台使用断点续传，从第{}页开始", platform, startPage);
                        } else {
                            logger.info("{}平台断点无效或过期，执行全量更新", platform);
                            needFullUpdate = true;
                        }
                    }

                    if (needFullUpdate) {
                        logger.info("{}平台执行全量商品数据更新", platform);
                        // 清理旧断点
                        checkpointManager.removeCheckpoint("goods_" + platform);
                        startPage = 1;
                    }

                    // 获取数据并等待完成
                    var fetchResult = dataFetchService.fetchGoodsData(platform, startPage, null).join();

                    // 记录执行结果
                    if (fetchResult.isSuccess()) {
                        logger.info("{}平台商品数据获取成功：获取{}条数据",
                            platform, fetchResult.getTotalRecords());
                    } else {
                        logger.warn("{}平台商品数据获取有错误：{}",
                            platform, fetchResult.errors());
                    }

                } catch (Exception e) {
                    logger.error("{}平台商品数据获取异常", platform, e);
                }
            }
        });
    }

    /**
     * 执行数据清理任务
     * 清理过期的价格历史记录和无效的断点
     *
     * @return 异步任务
     */
    public CompletableFuture<Void> executeDataCleanup() {
        return CompletableFuture.runAsync(() -> {
            try {
                logger.info("开始执行数据清理任务");

                // 1. 清理过期断点（超过7天的断点）
                int removedCheckpoints = checkpointManager.cleanupExpiredCheckpoints(7);
                logger.info("清理过期断点完成：删除{}个断点", removedCheckpoints);

                // 2. 清理过期价格历史记录（保留90天的数据）
                LocalDateTime cutoffTime = LocalDateTime.now().minusDays(90);
                // 这里可以添加价格历史清理逻辑，暂时跳过
                logger.info("价格历史数据清理：保留{}之后的数据", cutoffTime);

                // 3. 数据库统计信息
                var dbStats = dataStorageService.getDatabaseStats();
                logger.info("数据库统计：商品{}条，价格历史{}条",
                    dbStats.totalGoods(), dbStats.totalPriceHistory());

                // 4. 检查数据一致性
                List<String> platforms = properties.platform().defaultPlatforms();
                for (String platform : platforms) {
                    long goodsCount = dataStorageService.countGoodsByPlatform(platform);
                    logger.info("{}平台商品数量：{}", platform, goodsCount);
                }

                logger.info("数据清理任务完成");

            } catch (Exception e) {
                logger.error("数据清理任务异常", e);
            }
        });
    }

    /**
     * 手动执行指定平台的商品数据获取
     * 
     * @param platform 平台名称
     * @param startPage 起始页码
     * @param endPage 结束页码
     * @return 执行结果
     */
    public CompletableFuture<String> executeManualGoodsFetch(String platform, Integer startPage, Integer endPage) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("手动执行{}平台商品数据获取，页码范围：{}-{}", platform, startPage, endPage);
                
                var fetchResult = dataFetchService.fetchGoodsData(platform, startPage, endPage).join();
                var storageResult = dataStorageService.saveSkinGoods(fetchResult.data()).join();
                
                String result = String.format("平台：%s，获取：%d条，保存成功：%d条，错误：%d个", 
                    platform, fetchResult.getTotalRecords(), storageResult.successCount(), fetchResult.errors().size());
                
                logger.info("手动商品数据获取完成：{}", result);
                return result;
                
            } catch (Exception e) {
                String error = "手动商品数据获取异常：" + e.getMessage();
                logger.error(error, e);
                return error;
            }
        });
    }

    /**
     * 手动执行指定平台的价格数据获取
     * 注意：此方法保留用于向后兼容和特殊情况
     * 正常情况下，价格数据会在商品数据获取时自动提取
     *
     * @param platform 平台名称
     * @param date 日期
     * @return 执行结果
     */
    public CompletableFuture<String> executeManualPriceFetch(String platform, String date) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("手动执行{}平台价格数据获取，日期：{} (向后兼容模式)", platform, date);
                logger.warn("建议使用商品数据获取，价格数据会自动提取");

                List<Integer> goodsIds = dataStorageService.getGoodsIdsByPlatform(platform);
                if (goodsIds.isEmpty()) {
                    return String.format("平台：%s，无商品数据，请先执行商品数据获取", platform);
                }

                var fetchResult = dataFetchService.fetchPriceData(platform, goodsIds, date).join();
                var storageResult = dataStorageService.savePriceHistory(fetchResult.data()).join();

                String result = String.format("平台：%s，获取：%d条，保存成功：%d条，错误：%d个",
                    platform, fetchResult.getTotalRecords(), storageResult.successCount(), fetchResult.errors().size());

                logger.info("手动价格数据获取完成：{}", result);
                return result;

            } catch (Exception e) {
                String error = "手动价格数据获取异常：" + e.getMessage();
                logger.error(error, e);
                return error;
            }
        });
    }

    /**
     * 获取任务运行状态
     * 
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return isRunning.get();
    }

    /**
     * 停止当前运行的任务
     */
    public void stopCurrentTask() {
        if (isRunning.get()) {
            logger.info("请求停止当前运行的任务");
            // 这里可以添加更复杂的停止逻辑
            isRunning.set(false);
        }
    }

    /**
     * 获取任务统计信息
     *
     * @return 统计信息
     */
    public TaskStats getTaskStats() {
        var dbStats = dataStorageService.getDatabaseStats();
        var monitorStats = monitoringService.getExecutionStats();

        return new TaskStats(
            isRunning.get(),
            dbStats.totalGoods(),
            dbStats.totalPriceHistory(),
            dbStats.platformStats(),
            monitorStats
        );
    }

    /**
     * 检查是否需要执行全量更新
     *
     * @param platform 平台名称
     * @return 是否需要全量更新
     */
    private boolean shouldPerformFullUpdate(String platform) {
        try {
            // 检查商品数据的最后更新时间
            long goodsCount = dataStorageService.countGoodsByPlatform(platform);

            // 如果没有商品数据，必须全量更新
            if (goodsCount == 0) {
                logger.info("{}平台无商品数据，需要全量更新", platform);
                return true;
            }

            // 检查断点是否过期（根据配置的时间间隔强制全量更新）
            TaskCheckpoint checkpoint = checkpointManager.getCheckpoint("goods_" + platform);
            if (checkpoint != null) {
                LocalDateTime lastUpdate = checkpoint.lastUpdateTime();
                LocalDateTime now = LocalDateTime.now();
                long hoursSinceLastUpdate = java.time.Duration.between(lastUpdate, now).toHours();

                if (hoursSinceLastUpdate >= properties.scheduler().goodsFullUpdateHours()) {
                    logger.info("{}平台商品数据超过{}小时未全量更新，需要全量更新",
                        platform, properties.scheduler().goodsFullUpdateHours());
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            logger.warn("检查{}平台更新策略异常，默认执行全量更新", platform, e);
            return true;
        }
    }

    /**
     * 检查断点是否有效
     *
     * @param checkpoint 断点信息
     * @return 是否有效
     */
    private boolean isCheckpointValid(TaskCheckpoint checkpoint) {
        if (checkpoint == null) {
            return false;
        }

        // 检查断点是否过期（根据配置的过期时间）
        LocalDateTime now = LocalDateTime.now();
        long hoursSinceUpdate = java.time.Duration.between(checkpoint.lastUpdateTime(), now).toHours();

        if (hoursSinceUpdate > properties.scheduler().checkpointExpireHours()) {
            logger.info("断点已过期：{}小时前更新，超过{}小时阈值",
                hoursSinceUpdate, properties.scheduler().checkpointExpireHours());
            return false;
        }

        // 检查页码是否合理（不能小于1）
        if (checkpoint.lastPage() < 1) {
            logger.warn("断点页码无效：{}", checkpoint.lastPage());
            return false;
        }

        return true;
    }



    /**
     * 为指定平台执行商品数据获取
     *
     * @param platform 平台名称
     */
    private void executeGoodsDataFetchForPlatform(String platform) {
        try {
            logger.info("开始为{}平台获取商品数据", platform);

            // 检查是否需要全量更新
            boolean needFullUpdate = shouldPerformFullUpdate(platform);
            int startPage = 1;

            if (!needFullUpdate) {
                TaskCheckpoint checkpoint = checkpointManager.getCheckpoint("goods_" + platform);
                if (isCheckpointValid(checkpoint)) {
                    startPage = checkpoint.lastPage() + 1;
                }
            } else {
                checkpointManager.removeCheckpoint("goods_" + platform);
            }

            var fetchResult = dataFetchService.fetchGoodsData(platform, startPage, null).join();

            if (fetchResult.isSuccess()) {
                logger.info("{}平台商品数据获取成功：获取{}条数据",
                    platform, fetchResult.getTotalRecords());
            } else {
                logger.warn("{}平台商品数据获取有错误：{}",
                    platform, fetchResult.errors());
            }
        } catch (Exception e) {
            logger.error("{}平台商品数据获取异常", platform, e);
        }
    }

    /**
     * 执行启动时的数据获取任务
     * 根据配置获取指定平台的数据
     */
    private void executeStartupDataFetch() {
        try {
            List<String> platforms = startupConfig.getPlatforms();

            for (String platform : platforms) {
                logger.info("开始获取{}平台启动数据", platform);

                // 根据配置决定是否强制全量更新
                if (startupConfig.isForceFullUpdate()) {
                    logger.info("{}平台执行强制全量更新", platform);
                    dataFetchService.fetchGoodsData(platform, 1, null).join();
                } else {
                    // 使用现有的智能更新逻辑
                    executeGoodsDataFetchForPlatform(platform);
                }
            }
        } catch (Exception e) {
            logger.error("启动时数据获取执行失败", e);
            throw new RuntimeException("启动时数据获取失败", e);
        }
    }

    /**
     * 任务统计信息
     */
    public record TaskStats(
        boolean isRunning,
        long totalGoods,
        long totalPriceHistory,
        java.util.Map<String, Long> platformStats,
        Object executionStats
    ) {}
}
