package com.skin86.api.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.skin86.api.config.Skin86Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 断点管理器
 * 负责管理任务执行的断点信息
 * 支持断点续传和任务状态持久化
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@Component
public class CheckpointManager {

    private static final Logger logger = LoggerFactory.getLogger(CheckpointManager.class);
    private final ObjectMapper objectMapper;
    private final Skin86Properties properties;
    private final Map<String, TaskCheckpoint> checkpoints = new ConcurrentHashMap<>();
    private final Path checkpointFilePath;

    public CheckpointManager(ObjectMapper objectMapper, Skin86Properties properties) {
        this.objectMapper = objectMapper;
        this.properties = properties;
        this.checkpointFilePath = Paths.get(properties.scheduler().checkpointFile());
        
        // 启动时加载断点信息
        loadCheckpoints();
        
        // 定期保存断点信息
        startPeriodicSave();
    }

    /**
     * 获取任务断点
     * 
     * @param taskKey 任务键
     * @return 断点信息，如果不存在返回null
     */
    public TaskCheckpoint getCheckpoint(String taskKey) {
        return checkpoints.get(taskKey);
    }

    /**
     * 更新任务断点
     * 
     * @param taskKey 任务键
     * @param checkpoint 断点信息
     */
    public void updateCheckpoint(String taskKey, TaskCheckpoint checkpoint) {
        checkpoints.put(taskKey, checkpoint);
        logger.debug("更新断点：{} -> {}", taskKey, checkpoint);
        
        // 立即保存重要断点
        if (checkpoint.processedCount() > 0) {
            saveCheckpoints();
        }
    }

    /**
     * 删除任务断点
     * 
     * @param taskKey 任务键
     */
    public void removeCheckpoint(String taskKey) {
        TaskCheckpoint removed = checkpoints.remove(taskKey);
        if (removed != null) {
            logger.info("删除断点：{}", taskKey);
            saveCheckpoints();
        }
    }

    /**
     * 清除所有断点
     */
    public void clearAllCheckpoints() {
        checkpoints.clear();
        logger.info("清除所有断点");
        saveCheckpoints();
    }

    /**
     * 获取所有断点信息
     * 
     * @return 断点映射
     */
    public Map<String, TaskCheckpoint> getAllCheckpoints() {
        return Map.copyOf(checkpoints);
    }

    /**
     * 检查任务是否有断点
     * 
     * @param taskKey 任务键
     * @return 是否存在断点
     */
    public boolean hasCheckpoint(String taskKey) {
        return checkpoints.containsKey(taskKey);
    }

    /**
     * 创建任务断点
     * 
     * @param platform 平台
     * @param taskType 任务类型
     * @param lastPage 最后处理的页码
     * @param processedCount 已处理数量
     * @return 断点信息
     */
    public TaskCheckpoint createCheckpoint(String platform, String taskType, int lastPage, int processedCount) {
        return new TaskCheckpoint(platform, taskType, lastPage, LocalDateTime.now(), processedCount);
    }

    /**
     * 从文件加载断点信息
     */
    private void loadCheckpoints() {
        if (!Files.exists(checkpointFilePath)) {
            logger.info("断点文件不存在，使用空断点");
            return;
        }

        try {
            String content = Files.readString(checkpointFilePath);
            if (content.trim().isEmpty()) {
                logger.info("断点文件为空");
                return;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> loaded = objectMapper.readValue(content, Map.class);
            
            // 转换为正确的类型
            for (Map.Entry<String, Object> entry : loaded.entrySet()) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> checkpointData = (Map<String, Object>) entry.getValue();
                    
                    TaskCheckpoint checkpoint = new TaskCheckpoint(
                        (String) checkpointData.get("platform"),
                        (String) checkpointData.get("taskType"),
                        (Integer) checkpointData.get("lastPage"),
                        LocalDateTime.parse((String) checkpointData.get("lastUpdateTime")),
                        (Integer) checkpointData.get("processedCount")
                    );
                    
                    checkpoints.put(entry.getKey(), checkpoint);
                } catch (Exception e) {
                    logger.warn("解析断点数据失败：{}", entry.getKey(), e);
                }
            }
            
            logger.info("加载断点信息完成，共{}个断点", checkpoints.size());
            
        } catch (IOException e) {
            logger.error("加载断点文件失败", e);
        }
    }

    /**
     * 保存断点信息到文件
     */
    private void saveCheckpoints() {
        try {
            String content = objectMapper.writeValueAsString(checkpoints);
            Files.writeString(checkpointFilePath, content);
            logger.debug("保存断点信息完成，共{}个断点", checkpoints.size());
            
        } catch (IOException e) {
            logger.error("保存断点文件失败", e);
        }
    }

    /**
     * 启动定期保存任务
     */
    private void startPeriodicSave() {
        if (!properties.scheduler().enableCheckpoint()) {
            logger.info("断点功能已禁用");
            return;
        }

        // 使用虚拟线程定期保存
        Thread.ofVirtual().start(() -> {
            while (true) {
                try {
                    Thread.sleep(properties.scheduler().checkpointIntervalMinutes() * 60 * 1000L);
                    if (!checkpoints.isEmpty()) {
                        saveCheckpoints();
                    }
                } catch (InterruptedException e) {
                    logger.info("断点保存线程被中断");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("定期保存断点异常", e);
                }
            }
        });
    }

    /**
     * 获取断点统计信息
     * 
     * @return 统计信息
     */
    public CheckpointStats getCheckpointStats() {
        int totalCheckpoints = checkpoints.size();
        int activeCheckpoints = 0;
        LocalDateTime oldestCheckpoint = null;
        LocalDateTime newestCheckpoint = null;

        for (TaskCheckpoint checkpoint : checkpoints.values()) {
            if (checkpoint.lastUpdateTime().isAfter(LocalDateTime.now().minusHours(24))) {
                activeCheckpoints++;
            }

            if (oldestCheckpoint == null || checkpoint.lastUpdateTime().isBefore(oldestCheckpoint)) {
                oldestCheckpoint = checkpoint.lastUpdateTime();
            }

            if (newestCheckpoint == null || checkpoint.lastUpdateTime().isAfter(newestCheckpoint)) {
                newestCheckpoint = checkpoint.lastUpdateTime();
            }
        }

        return new CheckpointStats(totalCheckpoints, activeCheckpoints, oldestCheckpoint, newestCheckpoint);
    }

    /**
     * 清理过期断点
     * 
     * @param daysToKeep 保留天数
     * @return 清理的断点数量
     */
    public int cleanupExpiredCheckpoints(int daysToKeep) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(daysToKeep);
        
        int removedCount = 0;
        var iterator = checkpoints.entrySet().iterator();
        
        while (iterator.hasNext()) {
            var entry = iterator.next();
            if (entry.getValue().lastUpdateTime().isBefore(cutoffTime)) {
                iterator.remove();
                removedCount++;
                logger.debug("清理过期断点：{}", entry.getKey());
            }
        }
        
        if (removedCount > 0) {
            saveCheckpoints();
            logger.info("清理过期断点完成，删除{}个断点", removedCount);
        }
        
        return removedCount;
    }

    /**
     * 断点统计信息
     */
    public record CheckpointStats(
        int totalCheckpoints,
        int activeCheckpoints,
        LocalDateTime oldestCheckpoint,
        LocalDateTime newestCheckpoint
    ) {}
}
