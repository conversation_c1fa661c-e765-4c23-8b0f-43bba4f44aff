package com.skin86.api.scheduler;

import java.time.LocalDateTime;

/**
 * 任务断点信息
 * 
 * 使用Java 21 Record类定义任务执行的断点数据
 * 
 * @param platform 平台名称
 * @param taskType 任务类型（goods/price）
 * @param lastPage 最后处理的页码
 * @param lastUpdateTime 最后更新时间
 * @param processedCount 已处理的记录数量
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public record TaskCheckpoint(
    String platform,
    String taskType,
    int lastPage,
    LocalDateTime lastUpdateTime,
    int processedCount
) {
    
    /**
     * 创建商品获取任务断点
     * 
     * @param platform 平台名称
     * @param lastPage 最后处理的页码
     * @param processedCount 已处理数量
     * @return 断点信息
     */
    public static TaskCheckpoint forGoods(String platform, int lastPage, int processedCount) {
        return new TaskCheckpoint(platform, "goods", lastPage, LocalDateTime.now(), processedCount);
    }
    
    /**
     * 创建价格获取任务断点
     * 
     * @param platform 平台名称
     * @param processedCount 已处理数量
     * @return 断点信息
     */
    public static TaskCheckpoint forPrice(String platform, int processedCount) {
        return new TaskCheckpoint(platform, "price", 0, LocalDateTime.now(), processedCount);
    }
    
    /**
     * 更新处理进度
     * 
     * @param newPage 新的页码
     * @param additionalCount 新增处理数量
     * @return 更新后的断点
     */
    public TaskCheckpoint updateProgress(int newPage, int additionalCount) {
        return new TaskCheckpoint(
            platform, 
            taskType, 
            newPage, 
            LocalDateTime.now(), 
            processedCount + additionalCount
        );
    }
    
    /**
     * 检查断点是否过期
     * 
     * @param hours 过期小时数
     * @return 是否过期
     */
    public boolean isExpired(int hours) {
        return lastUpdateTime.isBefore(LocalDateTime.now().minusHours(hours));
    }
    
    /**
     * 获取任务键
     * 
     * @return 任务键
     */
    public String getTaskKey() {
        return taskType + "_" + platform;
    }
}
