version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: skin86-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-Skin86Root123!}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-cs2_skin_platform}
      MYSQL_USER: ${MYSQL_USER:-skin86}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-Skin86123@}
      TZ: Asia/Shanghai
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    networks:
      - skin86-network
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --max_connections=1000
      --innodb_buffer_pool_size=1G
      --innodb_log_file_size=256M
      --innodb_flush_log_at_trx_commit=2
      --slow_query_log=1
      --long_query_time=2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-Skin86Root123!}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: skin86-redis
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - skin86-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Skin86 API应用服务
  skin86-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: skin86-api
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # 数据库配置
      SPRING_DATASOURCE_URL: ***********************/${MYSQL_DATABASE:-cs2_skin_platform}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&useUnicode=true&characterEncoding=utf8&autoReconnect=true&failOverReadOnly=false&maxReconnects=10&rewriteBatchedStatements=true&cachePrepStmts=true&prepStmtCacheSize=250&prepStmtCacheSqlLimit=2048
      SPRING_DATASOURCE_USERNAME: ${MYSQL_USER:-skin86}
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD:-Skin86123@}
      
      # 应用配置
      SERVER_PORT: 8989
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-docker}
      
      # 安全配置
      API_KEY_ENABLED: ${API_KEY_ENABLED:-true}
      API_KEY_1: ${API_KEY_1:-sk-skin86-docker-12345678}
      API_KEY_2: ${API_KEY_2:-sk-skin86-prod-87654321}
      RATE_LIMIT_ENABLED: ${RATE_LIMIT_ENABLED:-true}
      RATE_LIMIT_MINUTE: ${RATE_LIMIT_MINUTE:-60}
      RATE_LIMIT_HOUR: ${RATE_LIMIT_HOUR:-1000}
      RATE_LIMIT_DAY: ${RATE_LIMIT_DAY:-10000}
      IP_WHITELIST_ENABLED: ${IP_WHITELIST_ENABLED:-false}
      
      # 性能配置
      API_MAX_CONCURRENCY: ${API_MAX_CONCURRENCY:-20}
      API_DELAY_MS: ${API_DELAY_MS:-100ms}
      DB_CONNECTION_POOL_SIZE: ${DB_CONNECTION_POOL_SIZE:-50}
      DB_GOODS_BATCH_SIZE: ${DB_GOODS_BATCH_SIZE:-1000}
      DB_PRICE_BATCH_SIZE: ${DB_PRICE_BATCH_SIZE:-500}
      
      # 启动配置
      STARTUP_FETCH_ENABLED: ${STARTUP_FETCH_ENABLED:-false}
      STARTUP_DELAY_SECONDS: ${STARTUP_DELAY_SECONDS:-10}
      STARTUP_PLATFORMS: ${STARTUP_PLATFORMS:-yp}
      
      # JVM配置
      JAVA_OPTS: "-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -Djdk.virtualThreadScheduler.parallelism=20"
      
      # 时区
      TZ: Asia/Shanghai
    ports:
      - "${APP_PORT:-8989}:8989"
    volumes:
      - app_logs:/app/logs
      - ./docker/app/config:/app/config
    networks:
      - skin86-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8989/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: skin86-nginx
    restart: unless-stopped
    depends_on:
      - skin86-api
    environment:
      TZ: Asia/Shanghai
    ports:
      - "${NGINX_HTTP_PORT:-80}:80"
      - "${NGINX_HTTPS_PORT:-443}:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - skin86-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  skin86-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local
