# 获取指定饰品名渠道价格

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /api/v1/skin/goods/third/today_price:
    post:
      summary: 获取指定饰品名渠道价格
      deprecated: false
      description: "style_name可选项字典：\nvar SkinGoodsMetaphysicsMap = map[string]string{\n\t\"79%-90%\":              \"79% - 90%\",\n\t\"80%-80.4%\":            \"80% - 80.4%\",\n\t\"80%-90%\":              \"80% - 90%\",\n\t\"80.4%-81%\":            \"80.4% - 81%\",\n\t\"81%-90%\":              \"81% - 90%\",\n\t\"90%-93%\":              \"90% - 93%\",\n\t\"93%-95%\":              \"93% - 95%\",\n\t\"95%-97%\":              \"95% - 97%\",\n\t\"97%-99%\":              \"97% - 99%\",\n\t\"99%-100%\":             \"99% - 100%\",\n\t\"Phase1\":               \"Phase1\",\n\t\"Phase2\":               \"Phase2\",\n\t\"Phase3\":               \"Phase3\",\n\t\"Phase4\":               \"Phase4\",\n\t\"T1\":                   \"T1\",\n\t\"T2\":                   \"T2\",\n\t\"T3\":                   \"T3\",\n\t\"T4\":                   \"T4\",\n\t\"Tier1\":                \"一档\",\n\t\"Tier2\":                \"二档\",\n\t\"Tier3\":                \"三档\",\n\t\"Tier4\":                \"四档\",\n\t\"Tier5\":                \"五档\",\n\t\"Tier6\":                \"六档\",\n\t\"Tier7\":                \"七档\",\n\t\"Tier8\":                \"八档\",\n\t\"Tier9\":                \"九档\",\n\t\"Tier10\":               \"十档\",\n\t\"ColdBee\":              \"冷蜂\",\n\t\"Single-sidedFullBlue\": \"单面全蓝\",\n\t\"EmbarrassedFace\":      \"囧脸\",\n\t\"StrongMan\":            \"壮汉\",\n\t\"OfficialPicture\":      \"官图\",\n\t\"SurprisedFace\":        \"惊讶脸\",\n\t\"HandleWithLittleStar\": \"握把小星星\",\n\t\"WarmBee\":              \"暖蜂\",\n\t\"GunHeadMoon\":          \"枪头月\",\n\t\"Togepi\":               \"波克比\",\n\t\"FunnyFace\":            \"滑稽脸\",\n\t\"FullRipple\":           \"满波纹\",\n\t\"SmileyFace\":           \"笑脸\",\n\t\"Ruby\":                 \"红宝石\",\n\t\"Emerald\":              \"绿宝石\",\n\t\"Sapphire\":             \"蓝宝石\",\n\t\"BlackPearl\":           \"黑珍珠\",\n}"
      tags:
        - 提供第三方调用饰品价格数据
      parameters:
        - name: appId
          in: header
          description: 固定appId
          required: false
          example: '002'
          schema:
            type: string
        - name: timestamp
          in: header
          description: 当前时间戳
          required: false
          example: '1733209408'
          schema:
            type: string
            default: '1733209408'
        - name: nonce
          in: header
          description: 随机字符串
          required: false
          example: random_string
          schema:
            type: string
            default: random_string
        - name: signature
          in: header
          description: 接口签名
          required: false
          example: FLl88auVD1vHXXOGEF4HxVLHPzyidZaieKdt/q4srNo=
          schema:
            type: string
            default: FLl99auVD1vHXXOGEF4HxVLHPzyidZaieKdt/q4srNo=
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                market_hash_name:
                  type: string
                  description: 饰品英文名
                platform:
                  type: string
                  description: '渠道平台: BUFF / IGXE / STEAM / YP'
                date:
                  type: string
                  description: 获取指定日期的价格数据，不传则返回当天
                style_name:
                  type: string
                  description: 玄学标签，例如:T1, Ruby
              required:
                - market_hash_name
                - platform
                - date
              x-apifox-orders:
                - market_hash_name
                - platform
                - date
                - style_name
            example:
              market_hash_name: USP-S | Whiteout (Minimal Wear)
              platform: BUFF
              date: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      price:
                        type: number
                        description: 最新/平均价格
                      max_price:
                        type: number
                        description: 最高价
                      min_price:
                        type: number
                        description: 最低价
                      updated_at:
                        type: string
                    required:
                      - price
                      - max_price
                      - min_price
                      - updated_at
                    x-apifox-orders:
                      - price
                      - max_price
                      - min_price
                      - updated_at
                required:
                  - code
                  - message
                  - data
                x-apifox-orders:
                  - code
                  - message
                  - data
              example:
                code: 0
                message: Ok
                data:
                  price: 194.72
                  max_price: 195.61
                  min_price: 193.97
                  updated_at: 2023-12-14 00:05:07 +0800 CST
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 提供第三方调用饰品价格数据
      x-apifox-status: released
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: https://csdata-api.skin86.com
    description: 正式环境
security: []

```
