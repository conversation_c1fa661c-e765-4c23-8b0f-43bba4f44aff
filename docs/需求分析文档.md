# Skin86 API 数据获取系统 - 需求分析文档

## 文档信息
- **项目名称**: Skin86 API 数据获取系统
- **文档版本**: v1.0
- **创建日期**: 2025-01-23
- **文档类型**: 需求分析与技术方案
- **目标**: Java 21 重写技术方案设计

---

## 1. 项目概述和目标

### 1.1 项目背景
Skin86 API 数据获取系统是一个专门用于采集和管理CS2（Counter-Strike 2）游戏饰品数据的自动化系统。该系统通过调用Skin86对外API接口，实现多平台饰品数据的批量获取、存储和管理。

### 1.2 核心目标
1. **数据采集自动化**: 实现多平台（BUFF、IGXE、STEAM、YP）饰品数据的自动化采集
2. **数据存储管理**: 建立完整的饰品信息和价格历史数据存储体系
3. **高性能处理**: 支持大规模数据的高并发获取和批量处理
4. **系统可靠性**: 提供断点续传、错误重试、数据验证等可靠性保障
5. **运维友好**: 支持灵活的调度配置、监控统计和日志管理

### 1.3 业务价值
- **市场分析**: 为CS2饰品市场提供实时价格数据和趋势分析
- **投资决策**: 支持饰品投资的数据驱动决策
- **价格监控**: 实现多平台价格对比和异常价格预警
- **数据服务**: 为第三方应用提供标准化的饰品数据服务

### 1.4 应用场景
1. **定时数据同步**: 按固定间隔自动同步最新的饰品数据
2. **实时价格监控**: 监控特定饰品的价格变化和市场趋势
3. **批量数据分析**: 支持大规模历史数据的统计分析
4. **API数据服务**: 为其他系统提供饰品数据查询接口

---

## 2. 功能需求清单

### 2.1 数据获取模块

#### 2.1.1 API接口调用
- **接口认证**: 支持HMAC-SHA256签名认证机制
- **多接口支持**: 
  - 获取指定饰品价格 (`/api/v1/skin/goods/third/today_price`)
  - 获取饰品列表 (`/api/v1/skin/goods/third/list`)
  - 批量获取价格 (`/api/v1/skin/goods/third/prices`)
- **参数配置**: 支持搜索条件、分页参数、日期范围等配置
- **错误处理**: 完善的API调用错误处理和重试机制

#### 2.1.2 多平台支持
- **平台覆盖**: BUFF、IGXE、STEAM、YP四大主流平台
- **平台配置**: 每个平台独立的并发数、延迟、批量大小配置
- **数据格式**: 统一的数据格式处理和平台差异兼容

#### 2.1.3 并发控制
- **并发限制**: 可配置的最大并发请求数（默认3）
- **请求延迟**: 可配置的请求间隔时间（默认1000ms）
- **流量控制**: 避免触发API限流的智能流量控制

### 2.2 数据存储模块

#### 2.2.1 数据库设计
- **饰品主表** (`skin_goods`): 存储饰品基本信息
  - 商品ID、平台ID、名称、价格、稀有度等
  - 支持多平台数据的统一存储
- **价格历史表** (`price_history`): 存储价格变化历史
  - 价格记录、变化趋势、交易量、数据质量等
  - 支持时间序列数据的高效查询

#### 2.2.2 数据操作
- **批量插入**: 高性能的批量数据插入操作
- **数据更新**: 支持增量更新和全量更新
- **数据验证**: 完整的数据格式验证和异常数据处理
- **事务管理**: 保证数据一致性的事务处理

### 2.3 调度管理模块

#### 2.3.1 任务调度
- **定时执行**: 支持多种时间间隔的定时任务（6h/12h/24h等）
- **执行模式**: 
  - 商品数据获取模式
  - 价格数据获取模式  
  - 混合模式（商品+价格）
- **任务管理**: 任务启动、停止、状态监控

#### 2.3.2 断点续传
- **进度保存**: 自动保存任务执行进度
- **故障恢复**: 支持从中断点恢复任务执行
- **状态管理**: 完整的任务状态跟踪和管理

### 2.4 配置管理模块

#### 2.4.1 配置系统
- **分层配置**: 默认配置 → 环境变量 → 命令行参数
- **环境支持**: 开发、测试、生产环境的差异化配置
- **热更新**: 支持配置的动态更新（部分配置）

#### 2.4.2 参数配置
- **API配置**: AppID、AppSecret、BaseURL、超时时间
- **数据库配置**: 连接信息、连接池、事务超时
- **性能配置**: 并发数、延迟、批量大小、重试次数
- **监控配置**: 日志级别、统计间隔、内存阈值

### 2.5 监控统计模块

#### 2.5.1 执行统计
- **API统计**: 请求总数、成功率、失败率、响应时间
- **数据统计**: 获取记录数、存储记录数、验证错误数
- **性能统计**: 处理速度、内存使用、并发效率

#### 2.5.2 日志管理
- **操作日志**: 详细的操作过程记录
- **错误日志**: 异常信息和错误堆栈
- **调度日志**: 任务调度和执行状态记录

---

## 3. 非功能性需求

### 3.1 性能需求

#### 3.1.1 吞吐量要求
- **数据处理**: 支持500条/批次的数据处理能力
- **并发处理**: 支持最大10个并发API请求
- **批量操作**: 数据库批量插入性能 > 1000条/秒

#### 3.1.2 响应时间要求
- **API调用**: 单次API调用超时时间 ≤ 30秒
- **数据库操作**: 单次数据库操作超时时间 ≤ 60秒
- **任务执行**: 单个平台数据获取完成时间 ≤ 30分钟

#### 3.1.3 资源使用要求
- **内存使用**: 系统内存使用率 ≤ 80%
- **CPU使用**: 平均CPU使用率 ≤ 70%
- **磁盘IO**: 数据库写入不影响系统响应性能

### 3.2 可靠性需求

#### 3.2.1 错误处理
- **重试机制**: API调用失败自动重试（默认3次）
- **降级策略**: 部分平台失败不影响其他平台执行
- **异常恢复**: 系统异常后能够自动恢复执行

#### 3.2.2 数据一致性
- **事务保证**: 关键数据操作使用数据库事务
- **数据验证**: 完整的输入数据格式验证
- **冲突处理**: 并发数据更新的冲突检测和处理

#### 3.2.3 故障恢复
- **断点续传**: 任务中断后能从断点恢复
- **状态持久化**: 关键状态信息持久化存储
- **自动重启**: 进程异常退出后自动重启机制

### 3.3 安全需求

#### 3.3.1 API安全
- **签名认证**: 使用HMAC-SHA256签名保证API调用安全
- **时间戳验证**: 防止重放攻击的时间戳验证
- **参数加密**: 敏感参数的加密传输

#### 3.3.2 配置安全
- **敏感信息**: API密钥等敏感信息通过环境变量配置
- **权限控制**: 数据库访问权限最小化原则
- **日志安全**: 日志中不记录敏感信息

### 3.4 可维护性需求

#### 3.4.1 代码质量
- **模块化设计**: 清晰的模块划分和职责分离
- **代码规范**: 统一的代码风格和命名规范
- **文档完整**: 完整的API文档和使用说明

#### 3.4.2 运维支持
- **监控接口**: 提供健康检查和监控数据接口
- **配置管理**: 支持配置的集中管理和动态更新
- **日志分析**: 结构化日志便于问题定位和分析

---

## 4. 数据模型和API规范

### 4.1 数据库设计

#### 4.1.1 饰品主表 (skin_goods)
```sql
CREATE TABLE skin_goods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    goods_id INT NOT NULL,                    -- 商品ID
    platform_id VARCHAR(50),                 -- 平台ID
    market_name VARCHAR(255) NOT NULL,       -- 市场名称
    market_hash_name VARCHAR(255) NOT NULL,  -- 市场哈希名称
    sell_min_price DECIMAL(10, 2),          -- 最低售价
    sell_max_num INT,                        -- 最大售出数量
    sell_valuation DECIMAL(15, 2),          -- 售出估值
    buy_max_price DECIMAL(10, 2),           -- 最高收购价
    buy_max_num INT,                         -- 最大收购数量
    price_alter_percentage_7d DECIMAL(10, 4), -- 7天价格变化百分比
    price_alter_value_7d DECIMAL(10, 2),    -- 7天价格变化值
    category_group_name VARCHAR(100),        -- 分类组名称
    rarity_color VARCHAR(20),                -- 稀有度颜色
    icon_url VARCHAR(500),                   -- 图标URL
    is_follow BOOLEAN,                       -- 是否关注
    redirect_url VARCHAR(500),               -- 重定向URL
    exterior VARCHAR(50),                    -- 外观
    rarity VARCHAR(50),                      -- 稀有度
    platform VARCHAR(20),                   -- 平台
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_goods_id (goods_id),
    INDEX idx_platform (platform),
    INDEX idx_market_hash_name (market_hash_name),
    UNIQUE KEY uk_goods_platform (goods_id, platform)
);
```

#### 4.1.2 价格历史表 (price_history)
```sql
CREATE TABLE price_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    skin_goods_id INT NOT NULL,              -- 皮肤商品ID
    goods_name VARCHAR(300) NOT NULL,        -- 商品名称(冗余字段)
    price DECIMAL(12,2) NOT NULL,           -- 价格(人民币)
    price_usd DECIMAL(12,2),                -- 价格(美元)
    price_change DECIMAL(12,2),             -- 价格变化
    price_change_percent DECIMAL(8,4),      -- 价格变化百分比
    volume INT,                             -- 交易量
    data_source VARCHAR(50) NOT NULL,       -- 数据来源
    record_type TINYINT NOT NULL,           -- 记录类型
    record_time DATETIME NOT NULL,          -- 记录时间
    exchange_rate DECIMAL(8,4),             -- 汇率
    trend TINYINT,                          -- 市场趋势
    volatility DECIMAL(8,4),                -- 价格波动性
    is_abnormal TINYINT DEFAULT 0,          -- 是否异常价格
    abnormal_reason VARCHAR(200),           -- 异常原因
    data_quality_score TINYINT DEFAULT 90,  -- 数据质量评分
    extra_info TEXT,                        -- 扩展信息(JSON)
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_skin_goods_id (skin_goods_id),
    INDEX idx_data_source (data_source),
    INDEX idx_record_time (record_time),
    INDEX idx_price_time (price, record_time),
    FOREIGN KEY (skin_goods_id) REFERENCES skin_goods(id) ON DELETE CASCADE
);
```

### 4.2 API接口规范

#### 4.2.1 认证机制
所有API请求需要在Header中包含以下认证信息：
- `appId`: 应用ID
- `timestamp`: 时间戳（秒级）
- `nonce`: 随机字符串（12位）
- `signature`: HMAC-SHA256签名

#### 4.2.2 签名算法
```javascript
// 1. 构造参数对象
const params = {
    appId: 'your_app_id',
    timestamp: '1640995200',
    nonce: 'random_string',
    action: '/api/v1/skin/goods/third/today_price',
    version: 'v1'
};

// 2. 按key字典序排序并拼接
const keys = Object.keys(params).sort();
let signString = '';
keys.forEach(key => {
    signString += key + params[key];
});
signString += appSecret;

// 3. HMAC-SHA256签名
const signature = crypto.createHmac('sha256', appSecret)
    .update(signString)
    .digest('base64');
```

#### 4.2.3 接口列表

**1. 获取指定饰品价格**
- **URL**: `POST /api/v1/skin/goods/third/today_price`
- **请求体**:
```json
{
    "market_hash_name": "AK-47 | Neon Revolution (Minimal Wear)",
    "platform": "BUFF",
    "date": "2025-01-23",
    "style_name": "T1"
}
```

**2. 获取饰品列表**
- **URL**: `POST /api/v1/skin/goods/third/list`
- **请求体**:
```json
{
    "search": "USP-S | Whiteout (Minimal Wear)",
    "platform": "BUFF",
    "page": 1,
    "page_size": 10
}
```

**3. 批量获取价格**
- **URL**: `POST /api/v1/skin/goods/third/prices`
- **请求体**:
```json
{
    "platform": "BUFF",
    "page": 1,
    "page_size": 10,
    "date": "2025-01-23",
    "goods_ids": "3975,1,2,3"
}
```

#### 4.2.4 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "list": [...],
        "total": 1000,
        "page": 1,
        "page_size": 10
    }
}
```

---

## 5. Java 21重写技术方案

### 5.1 技术栈选择

#### 5.1.1 核心框架
- **Spring Boot 3.2+**: 现代化的Spring框架，支持Java 21
- **Spring WebFlux**: 响应式编程框架，提升并发性能
- **Spring Data JPA**: 简化数据访问层开发
- **Spring Scheduler**: 任务调度管理
- **Spring Security**: API认证和安全控制

#### 5.1.2 数据库技术
- **HikariCP 5.0+**: 高性能数据库连接池
- **MySQL Connector/J 8.0+**: MySQL数据库驱动
- **Flyway**: 数据库版本管理和迁移
- **Redis**: 缓存和分布式锁（可选）

#### 5.1.3 HTTP客户端
- **OkHttp 4.11+**: 高性能HTTP客户端
- **Jackson 2.15+**: JSON序列化/反序列化
- **Retrofit**: 类型安全的HTTP客户端（可选）

#### 5.1.4 监控和日志
- **Micrometer**: 应用监控指标收集
- **Logback**: 日志框架
- **Spring Boot Actuator**: 健康检查和监控端点
- **Prometheus**: 监控指标存储（可选）

### 5.2 架构设计

#### 5.2.1 分层架构
```
┌─────────────────────────────────────┐
│           Controller Layer          │  ← REST API接口层
├─────────────────────────────────────┤
│            Service Layer            │  ← 业务逻辑层
├─────────────────────────────────────┤
│          Repository Layer           │  ← 数据访问层
├─────────────────────────────────────┤
│           External Layer            │  ← 外部API调用层
└─────────────────────────────────────┘
```

#### 5.2.2 核心模块设计

**1. 配置管理模块**
```java
@ConfigurationProperties(prefix = "skin86")
public record Skin86Properties(
    ApiConfig api,
    DatabaseConfig database,
    PerformanceConfig performance,
    SchedulerConfig scheduler
) {
    public record ApiConfig(
        String appId,
        String appSecret,
        String baseUrl,
        Duration timeout
    ) {}
    
    public record PerformanceConfig(
        int maxConcurrency,
        Duration delayMs,
        int retryTimes,
        int batchSize
    ) {}
}
```

**2. 数据获取服务**
```java
@Service
public class DataFetchService {
    
    @Async("dataFetchExecutor")
    public CompletableFuture<FetchResult> fetchGoodsData(
        Platform platform, 
        FetchOptions options
    ) {
        return CompletableFuture.supplyAsync(() -> {
            // 使用虚拟线程处理数据获取
            return Thread.ofVirtual().start(() -> {
                // 数据获取逻辑
            });
        });
    }
}
```

**3. 响应式数据处理**
```java
@Service
public class ReactiveDataProcessor {
    
    public Flux<SkinGoods> processGoodsData(Flux<RawData> rawDataFlux) {
        return rawDataFlux
            .buffer(batchSize)
            .flatMap(this::validateAndTransform)
            .onErrorContinue(this::handleError);
    }
}
```

### 5.3 Java 21新特性应用

#### 5.3.1 虚拟线程 (Project Loom)
```java
@Configuration
public class VirtualThreadConfig {
    
    @Bean
    public Executor virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
}

// 在数据获取中使用虚拟线程
public class DataFetcher {
    public void fetchDataConcurrently() {
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<Future<String>> futures = platforms.stream()
                .map(platform -> executor.submit(() -> fetchFromPlatform(platform)))
                .toList();
            
            // 等待所有任务完成
            futures.forEach(future -> {
                try {
                    future.get();
                } catch (Exception e) {
                    log.error("Task failed", e);
                }
            });
        }
    }
}
```

#### 5.3.2 Record类和Pattern Matching
```java
// 使用Record简化数据模型
public record ApiResponse<T>(
    int code,
    String message,
    T data,
    long timestamp
) {
    public boolean isSuccess() {
        return code == 200;
    }
}

// Pattern Matching优化条件判断
public String processResponse(ApiResponse<?> response) {
    return switch (response.code()) {
        case 200 -> "Success";
        case 400 -> "Bad Request";
        case 401 -> "Unauthorized";
        case 500 -> "Server Error";
        default -> "Unknown Error: " + response.code();
    };
}
```

#### 5.3.3 Sealed Classes增强类型安全
```java
public sealed interface FetchResult 
    permits SuccessResult, ErrorResult, PartialResult {
}

public record SuccessResult(List<SkinGoods> data, int count) 
    implements FetchResult {}

public record ErrorResult(String error, Throwable cause) 
    implements FetchResult {}

public record PartialResult(List<SkinGoods> data, List<String> errors) 
    implements FetchResult {}
```

### 5.4 性能优化方案

#### 5.4.1 异步处理优化
```java
@Service
public class AsyncDataService {
    
    @Async
    public CompletableFuture<Void> processDataAsync(List<RawData> data) {
        return CompletableFuture
            .supplyAsync(() -> validateData(data))
            .thenCompose(this::transformData)
            .thenCompose(this::saveData)
            .exceptionally(this::handleError);
    }
}
```

#### 5.4.2 缓存策略
```java
@Service
public class CacheService {
    
    @Cacheable(value = "goodsData", key = "#platform + '_' + #goodsId")
    public Optional<SkinGoods> getGoodsFromCache(Platform platform, Long goodsId) {
        return goodsRepository.findByPlatformAndGoodsId(platform, goodsId);
    }
    
    @CacheEvict(value = "goodsData", allEntries = true)
    @Scheduled(fixedRate = 3600000) // 1小时清理一次
    public void clearCache() {
        log.info("Cache cleared");
    }
}
```

#### 5.4.3 数据库优化
```java
@Repository
public class OptimizedGoodsRepository {
    
    @Modifying
    @Query(value = """
        INSERT INTO skin_goods (goods_id, platform, market_name, ...) 
        VALUES (:#{#goods.goodsId}, :#{#goods.platform}, :#{#goods.marketName}, ...)
        ON DUPLICATE KEY UPDATE 
        market_name = VALUES(market_name),
        updated_at = CURRENT_TIMESTAMP
        """, nativeQuery = true)
    void upsertGoods(@Param("goods") SkinGoods goods);
    
    @BatchSize(500)
    void saveAllInBatch(List<SkinGoods> goodsList);
}
```

### 5.5 部署和运维方案

#### 5.5.1 Docker容器化
```dockerfile
FROM openjdk:21-jdk-slim

WORKDIR /app
COPY target/skin86-api-fetcher.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "--enable-preview", "-jar", "app.jar"]
```

#### 5.5.2 Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: skin86-api-fetcher
spec:
  replicas: 3
  selector:
    matchLabels:
      app: skin86-api-fetcher
  template:
    metadata:
      labels:
        app: skin86-api-fetcher
    spec:
      containers:
      - name: app
        image: skin86/api-fetcher:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
```

#### 5.5.3 监控配置
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

logging:
  level:
    com.skin86: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

---

## 6. 实施建议和总结

### 6.1 实施阶段规划

#### 第一阶段：基础架构搭建（2-3周）
1. 搭建Spring Boot 3.x项目框架
2. 配置数据库连接和基础表结构
3. 实现配置管理和基础工具类
4. 搭建CI/CD流水线

#### 第二阶段：核心功能开发（4-5周）
1. 实现API客户端和签名机制
2. 开发数据获取和存储模块
3. 实现任务调度和断点续传
4. 添加监控和日志功能

#### 第三阶段：性能优化和测试（2-3周）
1. 性能调优和压力测试
2. 完善错误处理和重试机制
3. 集成测试和端到端测试
4. 文档完善和部署准备

#### 第四阶段：生产部署和运维（1-2周）
1. 生产环境部署和配置
2. 监控告警配置
3. 运维文档和操作手册
4. 用户培训和知识转移

### 6.2 技术风险和应对

#### 6.2.1 主要技术风险
1. **Java 21兼容性**: 部分第三方库可能不完全支持Java 21
2. **虚拟线程稳定性**: 虚拟线程作为新特性可能存在未知问题
3. **性能调优复杂性**: 响应式编程和异步处理的调优复杂度较高

#### 6.2.2 应对策略
1. **渐进式升级**: 先使用稳定特性，逐步引入新特性
2. **充分测试**: 建立完善的测试体系，包括单元测试、集成测试、性能测试
3. **监控告警**: 建立完善的监控体系，及时发现和处理问题
4. **回滚方案**: 准备回滚到Node.js版本的应急方案

### 6.3 预期收益

#### 6.3.1 性能提升
- **并发处理能力**: 虚拟线程支持更高的并发数
- **内存使用效率**: JVM内存管理优化，减少GC压力
- **响应时间**: 响应式编程提升系统响应性能

#### 6.3.2 开发效率
- **类型安全**: 静态类型检查减少运行时错误
- **代码质量**: 现代Java特性提升代码可读性和维护性
- **生态支持**: 丰富的Java生态和工具链支持

#### 6.3.3 运维优势
- **监控完善**: 成熟的JVM监控工具和APM支持
- **部署灵活**: 容器化部署和云原生支持
- **扩展性**: 更好的水平扩展和负载均衡支持

---

## 7. 结论

基于对现有Node.js系统的深入分析，Java 21重写方案具有以下优势：

1. **技术先进性**: 利用Java 21的虚拟线程、Record类等新特性
2. **性能优势**: 更好的并发处理能力和内存管理
3. **生态成熟**: 丰富的企业级框架和工具支持
4. **运维友好**: 完善的监控、部署和扩展方案

建议按照分阶段实施计划，稳步推进Java 21重写工作，在保证系统稳定性的前提下，充分发挥Java平台的技术优势，为业务发展提供更强大的技术支撑。

---

**文档状态**: 初稿完成  
**下一步**: 技术方案评审和实施计划确认
