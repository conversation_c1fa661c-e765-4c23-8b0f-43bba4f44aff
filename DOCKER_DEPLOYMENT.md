# Skin86 API数据获取系统 - Docker部署指南

## 📋 目录
- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [监控和日志](#监控和日志)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🔧 系统要求

### 最低要求
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB可用空间
- **操作系统**: Linux/Windows/macOS

### 推荐配置
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB SSD
- **网络**: 稳定的互联网连接

### 软件要求
- Docker 20.10+
- Docker Compose 2.0+

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd skin86-api
```

### 2. 一键部署

#### Linux/macOS
```bash
chmod +x deploy.sh
./deploy.sh
```

#### Windows
```cmd
deploy.bat
```

### 3. 访问服务
- **API文档**: http://localhost/swagger-ui/index.html
- **健康检查**: http://localhost/actuator/health
- **API端点**: http://localhost/api/v1/tasks/status
- **直接访问应用**: http://localhost:8989/swagger-ui/index.html

## ⚙️ 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env` 并根据需要修改：

```bash
cp .env.example .env
```

#### 主要配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `MYSQL_ROOT_PASSWORD` | `Skin86Root123!` | MySQL root密码 |
| `MYSQL_DATABASE` | `cs2_skin_platform` | 数据库名称 |
| `MYSQL_USER` | `skin86` | 应用数据库用户 |
| `MYSQL_PASSWORD` | `Skin86123@` | 应用数据库密码 |
| `API_KEY_1` | `sk-skin86-docker-12345678` | 开发环境API Key |
| `API_KEY_2` | `sk-skin86-prod-87654321` | 生产环境API Key |
| `APP_PORT` | `8080` | 应用端口 |
| `NGINX_HTTP_PORT` | `80` | Nginx HTTP端口 |

#### 安全配置
```env
API_KEY_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MINUTE=60
RATE_LIMIT_HOUR=1000
RATE_LIMIT_DAY=10000
IP_WHITELIST_ENABLED=false
```

#### 性能配置
```env
API_MAX_CONCURRENCY=20
API_DELAY_MS=100ms
DB_CONNECTION_POOL_SIZE=50
DB_GOODS_BATCH_SIZE=1000
DB_PRICE_BATCH_SIZE=500
```

## 🔄 服务管理

### 基本命令

```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f skin86-api
```

### 单独管理服务

```bash
# 只启动数据库
docker-compose up -d mysql

# 重启API服务
docker-compose restart skin86-api

# 停止Nginx
docker-compose stop nginx
```

### 数据备份和恢复

#### 备份数据库
```bash
docker-compose exec mysql mysqldump -u root -p cs2_skin_platform > backup.sql
```

#### 恢复数据库
```bash
docker-compose exec -T mysql mysql -u root -p cs2_skin_platform < backup.sql
```

## 📊 监控和日志

### 健康检查
```bash
# 检查所有服务健康状态
curl http://localhost/actuator/health

# 检查API服务
curl -H "X-API-Key: sk-skin86-docker-12345678" http://localhost/api/v1/tasks/status
```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 实时查看API服务日志
docker-compose logs -f skin86-api

# 查看最近100行日志
docker-compose logs --tail=100 skin86-api
```

### 性能监控
```bash
# 查看容器资源使用情况
docker stats

# 查看特定容器资源使用
docker stats skin86-api
```

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 查看端口占用
netstat -tulpn | grep :8080

# 修改端口配置
# 编辑 .env 文件中的 APP_PORT
```

#### 2. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose ps mysql

# 查看MySQL日志
docker-compose logs mysql

# 重启MySQL服务
docker-compose restart mysql
```

#### 3. 内存不足
```bash
# 查看系统内存使用
free -h

# 调整JVM内存配置
# 编辑 docker-compose.yml 中的 JAVA_OPTS
```

#### 4. 镜像构建失败
```bash
# 清理Docker缓存
docker system prune -a

# 重新构建镜像
docker-compose build --no-cache
```

### 调试模式

启用调试模式：
```bash
# 设置调试环境变量
export SPRING_PROFILES_ACTIVE=debug

# 重启服务
docker-compose restart skin86-api
```

## 🚀 性能优化

### 1. JVM优化
```yaml
# docker-compose.yml 中的 JAVA_OPTS
JAVA_OPTS: "-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### 2. 数据库优化
```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL max_connections = 1000;
```

### 3. 网络优化
```yaml
# 使用自定义网络
networks:
  skin86-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### 4. 存储优化
```yaml
# 使用命名卷
volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/skin86/mysql
```

## 📈 扩展部署

### 水平扩展
```bash
# 扩展API服务实例
docker-compose up -d --scale skin86-api=3
```

### 负载均衡
```nginx
# nginx配置
upstream skin86_api {
    server skin86-api_1:8080;
    server skin86-api_2:8080;
    server skin86-api_3:8080;
}
```

## 🔒 安全建议

1. **修改默认密码**: 更改所有默认密码
2. **启用HTTPS**: 配置SSL证书
3. **网络隔离**: 使用防火墙限制访问
4. **定期更新**: 保持镜像和依赖最新
5. **备份策略**: 定期备份数据和配置

## 📞 支持

如有问题，请联系：
- **邮箱**: <EMAIL>
- **文档**: [项目文档](https://docs.skin86.com)
- **问题反馈**: [GitHub Issues](https://github.com/skin86/api/issues)
