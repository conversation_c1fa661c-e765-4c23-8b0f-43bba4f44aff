#!/bin/bash

# Skin86 API数据获取系统 - Docker部署脚本
# 作者: Skin86 Team
# 版本: 1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建环境变量文件
setup_env() {
    if [ ! -f .env ]; then
        log_info "创建环境变量文件..."
        cp .env.example .env
        log_warning "请编辑 .env 文件配置您的环境变量"
        log_info "配置文件位置: $(pwd)/.env"
    else
        log_info "环境变量文件已存在"
    fi
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    docker-compose build --no-cache
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    docker-compose up -d
    log_success "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 等待服务启动
    sleep 30
    
    # 检查MySQL
    if docker-compose exec mysql mysqladmin ping -h localhost --silent; then
        log_success "MySQL服务正常"
    else
        log_error "MySQL服务异常"
    fi
    
    # 检查Redis
    if docker-compose exec redis redis-cli ping | grep -q PONG; then
        log_success "Redis服务正常"
    else
        log_warning "Redis服务异常（可选服务）"
    fi
    
    # 检查应用
    if curl -f http://localhost:8080/actuator/health &> /dev/null; then
        log_success "Skin86 API服务正常"
    else
        log_error "Skin86 API服务异常"
    fi
    
    # 检查Nginx
    if curl -f http://localhost/health &> /dev/null; then
        log_success "Nginx服务正常"
    else
        log_warning "Nginx服务异常（可选服务）"
    fi
}

# 显示服务信息
show_info() {
    log_info "服务信息:"
    echo "=================================="
    echo "🌐 API文档: http://localhost/swagger-ui/index.html"
    echo "🔍 健康检查: http://localhost/actuator/health"
    echo "📊 API端点: http://localhost/api/v1/tasks/status"
    echo "🗄️  MySQL: localhost:3306"
    echo "🔴 Redis: localhost:6379"
    echo "=================================="
    echo ""
    echo "📋 API Key (用于测试):"
    echo "   开发环境: sk-skin86-docker-12345678"
    echo "   生产环境: sk-skin86-prod-87654321"
    echo ""
    echo "📖 使用示例:"
    echo "   curl -H \"X-API-Key: sk-skin86-docker-12345678\" http://localhost/api/v1/tasks/status"
    echo ""
    echo "🔧 管理命令:"
    echo "   查看日志: docker-compose logs -f"
    echo "   停止服务: docker-compose down"
    echo "   重启服务: docker-compose restart"
    echo "=================================="
}

# 主函数
main() {
    echo "🚀 Skin86 API数据获取系统 - Docker部署脚本"
    echo "=============================================="
    
    check_requirements
    setup_env
    build_images
    start_services
    check_services
    show_info
    
    log_success "部署完成！"
}

# 处理命令行参数
case "${1:-}" in
    "build")
        build_images
        ;;
    "start")
        start_services
        ;;
    "stop")
        docker-compose down
        log_success "服务已停止"
        ;;
    "restart")
        docker-compose restart
        log_success "服务已重启"
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "status")
        check_services
        ;;
    "clean")
        docker-compose down -v --rmi all
        log_success "清理完成"
        ;;
    *)
        main
        ;;
esac
