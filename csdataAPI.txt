csdata API

app_id: "002"
app_secret: "E3E80D27D09873BB85B2BB1B9C3618A5"

curl --location --request POST 'https://csdata-api.skin86.com/api/v1/skin/goods/third/today_price' \
--header 'appId: 002' \
--header 'timestamp: 1733209408' \
--header 'nonce: random_string' \
--header 'signature: FLl88auVD1vHXXOGEF4HxVLHPzyidZaieKdt/q4srNo=' \
--header 'Content-Type: application/json' \
--data-raw '{
    "market_hash_name": "USP-S | Whiteout (Minimal Wear)",
    "platform": "BUFF",
    "date": "2025-06-10"
}'


golang sample

otherParams := map[string]interface{}{
		"action":  "/api/v1/skin/goods/third/today_price", //当前请求的url
		"version": "v1",     //接口url中的版本号，目前都是v1
	}
params := buildRequestParams(headers.Get("timestamp"), nonce, otherParams, conf)
headers.Set("signature", generateSignature(params, conf))

// 构造请求参数
func buildRequestParams(timestamp string, nonce string, otherParams map[string]interface{}, conf *conf.Csdata) map[string]interface{} {
	params := map[string]interface{}{
		"appId":     conf.GetAppId(),
		"timestamp": timestamp,
		"nonce":     nonce,
	}
	for k, v := range otherParams {
		params[k] = v
	}
	return params
}

// 签名算法: HMAC-SHA256
func generateSignature(params map[string]interface{}, conf *conf.Csdata) string {
	// 1. 将参数按照key的字典序排序
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 构造待签名的字符串
	var sb strings.Builder
	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(fmt.Sprintf("%s", params[k]))
	}
	sb.WriteString(conf.GetAppSecret())

	// 3. 使用 HMAC-SHA256 算法生成签名
	h := hmac.New(sha256.New, []byte(conf.GetAppSecret()))
	h.Write([]byte(sb.String()))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return signature
}