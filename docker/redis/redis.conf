# Redis配置文件 - 针对Skin86项目优化

# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 300

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# AOF配置
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志配置
loglevel notice
logfile ""

# 安全配置
# requirepass your_redis_password_here

# 性能配置
databases 16
tcp-backlog 511
hz 10

# 客户端配置
maxclients 10000

# 慢查询配置
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100
