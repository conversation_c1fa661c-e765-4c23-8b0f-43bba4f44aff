# Skin86 API数据获取系统 - 环境变量配置示例
# 复制此文件为 .env 并根据实际环境修改配置

# ===========================================
# 数据库配置
# ===========================================
MYSQL_ROOT_PASSWORD=Skin86Root123!
MYSQL_DATABASE=cs2_skin_platform
MYSQL_USER=skin86
MYSQL_PASSWORD=Skin86123@
MYSQL_PORT=3306

# ===========================================
# Redis配置
# ===========================================
REDIS_PORT=6379

# ===========================================
# 应用配置
# ===========================================
APP_PORT=8989
SPRING_PROFILES_ACTIVE=docker

# ===========================================
# 安全配置
# ===========================================
API_KEY_ENABLED=true
API_KEY_1=sk-skin86-docker-12345678
API_KEY_2=sk-skin86-prod-87654321
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MINUTE=60
RATE_LIMIT_HOUR=1000
RATE_LIMIT_DAY=10000
IP_WHITELIST_ENABLED=false

# ===========================================
# 性能配置
# ===========================================
API_MAX_CONCURRENCY=20
API_DELAY_MS=100ms
DB_CONNECTION_POOL_SIZE=50
DB_GOODS_BATCH_SIZE=1000
DB_PRICE_BATCH_SIZE=500

# ===========================================
# 启动配置
# ===========================================
STARTUP_FETCH_ENABLED=false
STARTUP_DELAY_SECONDS=10
STARTUP_PLATFORMS=yp

# ===========================================
# Nginx配置
# ===========================================
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# ===========================================
# 开发环境配置
# ===========================================
# 取消注释以下配置用于开发环境
# SPRING_PROFILES_ACTIVE=dev
# API_KEY_ENABLED=false
# RATE_LIMIT_ENABLED=false
# STARTUP_FETCH_ENABLED=true
