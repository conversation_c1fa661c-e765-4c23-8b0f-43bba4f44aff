@echo off
REM Skin86 API数据获取系统 - Windows Docker部署脚本
REM 作者: Skin86 Team
REM 版本: 1.0.0

setlocal enabledelayedexpansion

echo 🚀 Skin86 API数据获取系统 - Docker部署脚本
echo ==============================================

REM 检查Docker
echo [INFO] 检查Docker安装...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker Compose
echo [INFO] 检查Docker Compose安装...
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] 系统要求检查通过

REM 创建环境变量文件
if not exist .env (
    echo [INFO] 创建环境变量文件...
    copy .env.example .env >nul
    echo [WARNING] 请编辑 .env 文件配置您的环境变量
    echo [INFO] 配置文件位置: %CD%\.env
) else (
    echo [INFO] 环境变量文件已存在
)

REM 构建镜像
echo [INFO] 构建Docker镜像...
docker-compose build --no-cache
if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] 镜像构建完成

REM 启动服务
echo [INFO] 启动服务...
docker-compose up -d
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    pause
    exit /b 1
)
echo [SUCCESS] 服务启动完成

REM 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo [INFO] 检查服务状态...

REM 检查应用健康状态
curl -f http://localhost:8080/actuator/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Skin86 API服务可能还在启动中...
) else (
    echo [SUCCESS] Skin86 API服务正常
)

REM 检查Nginx
curl -f http://localhost/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Nginx服务可能还在启动中...
) else (
    echo [SUCCESS] Nginx服务正常
)

REM 显示服务信息
echo.
echo [INFO] 服务信息:
echo ==================================
echo 🌐 API文档: http://localhost/swagger-ui/index.html
echo 🔍 健康检查: http://localhost/actuator/health
echo 📊 API端点: http://localhost/api/v1/tasks/status
echo 🗄️  MySQL: localhost:3306
echo 🔴 Redis: localhost:6379
echo ==================================
echo.
echo 📋 API Key (用于测试):
echo    开发环境: sk-skin86-docker-12345678
echo    生产环境: sk-skin86-prod-87654321
echo.
echo 📖 使用示例:
echo    curl -H "X-API-Key: sk-skin86-docker-12345678" http://localhost/api/v1/tasks/status
echo.
echo 🔧 管理命令:
echo    查看日志: docker-compose logs -f
echo    停止服务: docker-compose down
echo    重启服务: docker-compose restart
echo ==================================

echo [SUCCESS] 部署完成！
echo.
echo 按任意键打开API文档...
pause >nul
start http://localhost/swagger-ui/index.html
