version: '3.8'

# 简化版Docker Compose配置 - 仅包含核心服务
services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: skin86-mysql-simple
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: Skin86Root123!
      MYSQL_DATABASE: cs2_skin_platform
      MYSQL_USER: skin86
      MYSQL_PASSWORD: Skin86123@
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_simple_data:/var/lib/mysql
    networks:
      - skin86-simple
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --max_connections=500
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pSkin86Root123!"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Skin86 API应用服务
  skin86-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: skin86-api-simple
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      # 数据库配置
      SPRING_DATASOURCE_URL: *********************************************************************************************************************************************************************************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: skin86
      SPRING_DATASOURCE_PASSWORD: Skin86123@
      
      # 应用配置
      SERVER_PORT: 8989
      SPRING_PROFILES_ACTIVE: docker
      
      # 安全配置
      API_KEY_ENABLED: true
      API_KEY_1: sk-skin86-docker-12345678
      API_KEY_2: sk-skin86-prod-87654321
      RATE_LIMIT_ENABLED: true
      RATE_LIMIT_MINUTE: 60
      RATE_LIMIT_HOUR: 1000
      RATE_LIMIT_DAY: 10000
      
      # 性能配置
      API_MAX_CONCURRENCY: 10
      API_DELAY_MS: 200ms
      DB_CONNECTION_POOL_SIZE: 20
      
      # 启动配置
      STARTUP_FETCH_ENABLED: false
      STARTUP_DELAY_SECONDS: 10
      
      # JVM配置
      JAVA_OPTS: "-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
      
      # 时区
      TZ: Asia/Shanghai
    ports:
      - "8989:8989"
    volumes:
      - app_simple_logs:/app/logs
    networks:
      - skin86-simple
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8989/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

# 网络配置
networks:
  skin86-simple:
    driver: bridge

# 数据卷配置
volumes:
  mysql_simple_data:
    driver: local
  app_simple_logs:
    driver: local
