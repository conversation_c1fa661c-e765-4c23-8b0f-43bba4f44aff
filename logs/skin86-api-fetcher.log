2025-08-26 21:38:47.212 [main] INFO  com.skin86.api.Application - Starting Application using Java 21.0.7 with PID 9716 (C:\Users\<USER>\Desktop\java\autoSkinData\bot-api\target\classes started by Administrator in C:\Users\<USER>\Desktop\java\autoSkinData\bot-api)
2025-08-26 21:38:47.221 [main] DEBUG com.skin86.api.Application - Running with Spring Boot v3.2.1, Spring v6.1.2
2025-08-26 21:38:47.223 [main] INFO  com.skin86.api.Application - The following 1 profile is active: "dev"
2025-08-26 21:38:49.079 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-26 21:38:49.080 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.17]
2025-08-26 21:38:49.161 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-26 21:38:49.458 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-26 21:38:49.744 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1c53bd49
2025-08-26 21:38:49.746 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-26 21:38:49.867 [main] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 9.22.3 by Redgate
2025-08-26 21:38:49.868 [main] INFO  o.f.c.i.license.VersionPrinter - See release notes here: https://rd.gt/416ObMi
2025-08-26 21:38:49.868 [main] INFO  o.f.c.i.license.VersionPrinter - 
2025-08-26 21:38:49.875 [main] INFO  o.f.c.i.r.ResourceNameValidator - 1 SQL migrations were detected but not run because they did not follow the filename convention.
2025-08-26 21:38:49.875 [main] INFO  o.f.c.i.r.ResourceNameValidator - Set 'validateMigrationNaming' to true to fail fast and see a list of the invalid file names.
2025-08-26 21:38:49.981 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************************* (MySQL 8.4)
2025-08-26 21:38:50.287 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.0.
2025-08-26 21:38:50.510 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 2 migrations (execution time 00:00.185s)
2025-08-26 21:38:50.849 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `cs2_skin_platform`: 2
2025-08-26 21:38:50.849 [main] WARN  o.f.core.internal.command.DbMigrate - Schema `cs2_skin_platform` has a version (2) that is newer than the latest available migration (1) !
2025-08-26 21:38:50.887 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `cs2_skin_platform` is up to date. No migration necessary.
2025-08-26 21:38:51.468 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-26 21:38:53.729 [main] INFO  c.s.api.scheduler.CheckpointManager - 断点文件不存在，使用空断点
2025-08-26 21:38:53.930 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-26 21:38:53.970 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 5153364c-55db-4aec-9727-9ab691539c2c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-26 21:38:55.082 [main] INFO  com.skin86.api.Application - Started Application in 8.272 seconds (process running for 8.608)
2025-08-26 21:38:55.144 [main] INFO  c.skin86.api.scheduler.TaskScheduler - 应用启动完成，检查启动时数据获取配置：StartupConfig{enabled=false, delaySeconds=5, platforms=[yp], forceFullUpdate=false, timeoutMinutes=30}
2025-08-26 21:38:55.146 [main] INFO  c.skin86.api.scheduler.TaskScheduler - 启动时数据获取已禁用，跳过自动执行
2025-08-26 21:39:03.740 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用83.30725860595703MB，活跃线程0.0，任务统计0
2025-08-26 21:39:13.746 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用83.30725860595703MB，活跃线程0.0，任务统计0
2025-08-26 21:39:23.757 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用83.38727569580078MB，活跃线程0.0，任务统计0
2025-08-26 21:39:33.769 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用83.38727569580078MB，活跃线程0.0，任务统计0
2025-08-26 21:39:38.864 [tomcat-handler-0] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-26 21:39:43.781 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用93.88182830810547MB，活跃线程0.0，任务统计0
2025-08-26 21:39:53.789 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用93.88182830810547MB，活跃线程0.0，任务统计0
2025-08-26 21:40:03.794 [] DEBUG c.s.api.service.MonitoringService - 当前统计信息：内存使用93.88182830810547MB，活跃线程0.0，任务统计0
