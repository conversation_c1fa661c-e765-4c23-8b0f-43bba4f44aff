{"openapi": "3.0.1", "info": {"title": "skin86对外接口", "description": "", "version": "1.0.0"}, "tags": [{"name": "bucks"}], "paths": {"/api/v1/skin/goods/third/today_price": {"post": {"summary": "获取指定饰品名渠道价格", "deprecated": false, "description": "", "tags": ["bucks"], "parameters": [{"name": "appId", "in": "header", "description": "", "example": "{{SKIN86_APPID}}", "schema": {"type": "string", "default": "{{SKIN86_APPID}}"}}, {"name": "timestamp", "in": "header", "description": "", "example": "{{$date.timestamp}}", "schema": {"type": "string", "default": "{{$date.timestamp}}"}}, {"name": "nonce", "in": "header", "description": "", "example": "{{$string.nanoid(min=12,max=12)}}", "schema": {"type": "string", "default": "{{$string.nanoid(min=12,max=12)}}"}}, {"name": "signature", "in": "header", "description": "", "example": "xxxx", "schema": {"type": "string", "default": "xxxx"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"market_hash_name": "USP-S | Whiteout (Minimal Wear)", "platform": "BUFF", "date": "2025-06-10"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/v1/skin/goods/third/list": {"post": {"summary": "获取饰品列表", "deprecated": false, "description": "", "tags": ["bucks"], "parameters": [{"name": "appId", "in": "header", "description": "", "example": "{{SKIN86_APPID}}", "schema": {"type": "string", "default": "{{SKIN86_APPID}}"}}, {"name": "timestamp", "in": "header", "description": "", "example": "{{$date.timestamp}}", "schema": {"type": "string", "default": "{{$date.timestamp}}"}}, {"name": "nonce", "in": "header", "description": "", "example": "{{$string.nanoid(min=12,max=12)}}", "schema": {"type": "string", "default": "{{$string.nanoid(min=12,max=12)}}"}}, {"name": "signature", "in": "header", "description": "", "example": "xxxx", "schema": {"type": "string", "default": "xxxx"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"search": "USP-S | Whiteout (Minimal Wear)", "platform": "BUFF", "page": 1, "page_size": 10}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {}}}, "required": ["code", "message", "data"]}}}, "headers": {}}}, "security": []}}, "/api/v1/skin/goods/third/prices": {"post": {"summary": "批量获取饰品渠道价格", "deprecated": false, "description": "", "tags": ["bucks"], "parameters": [{"name": "appId", "in": "header", "description": "", "example": "{{SKIN86_APPID}}", "schema": {"type": "string", "default": "{{SKIN86_APPID}}"}}, {"name": "timestamp", "in": "header", "description": "", "example": "{{$date.timestamp}}", "schema": {"type": "string", "default": "{{$date.timestamp}}"}}, {"name": "nonce", "in": "header", "description": "", "example": "{{$string.nanoid(min=12,max=12)}}", "schema": {"type": "string", "default": "{{$string.nanoid(min=12,max=12)}}"}}, {"name": "signature", "in": "header", "description": "", "example": "xxxx", "schema": {"type": "string", "default": "xxxx"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": {"platform": "BUFF", "page": 1, "page_size": 10, "date": "2025-06-17", "goods_ids": "3975,1,2,3"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [{"url": "https://csdata-api.skin86.com", "description": "开发环境"}], "security": []}